from fastapi import APIRouter

from .auth import router as auth_router
from .users import router as users_router
from .admin import router as admin_router
from .cantieri import router as cantieri_router
from .cavi import router as cavi_router
from .cavi_spare import router as cavi_spare_router
from .parco_cavi import router as parco_cavi_router
from .excel import router as excel_router
from .reports_simple import router as reports_router
from .strumenti import router as strumenti_router
from .certificazioni import router as certificazioni_router
from .comande import router as comande_router
from .rapporti_generali import router as rapporti_generali_router
from .prove_dettagliate import router as prove_dettagliate_router
from .non_conformita import router as non_conformita_router
from .responsabili import router as responsabili_router
from .pdf_cei_64_8 import router as pdf_cei_64_8_router
from .mobile import router as mobile_router
from .rapportini import router as rapportini_router

# Crea un router principale per l'API
api_router = APIRouter()

# Includi i router delle varie API
api_router.include_router(auth_router, prefix="/auth", tags=["auth"])
api_router.include_router(users_router, prefix="/users", tags=["users"])
api_router.include_router(admin_router, prefix="/admin", tags=["admin"])
api_router.include_router(cantieri_router, prefix="/cantieri", tags=["cantieri"])
api_router.include_router(cavi_router, prefix="/cavi", tags=["cavi"])
api_router.include_router(cavi_spare_router, prefix="/cavi", tags=["cavi"])
api_router.include_router(parco_cavi_router, prefix="/parco-cavi", tags=["parco-cavi"])
api_router.include_router(excel_router, prefix="/excel", tags=["excel"])
api_router.include_router(reports_router, prefix="/reports", tags=["reports"])
api_router.include_router(strumenti_router, prefix="/cantieri", tags=["strumenti"])
api_router.include_router(certificazioni_router, prefix="/cantieri", tags=["certificazioni"])
api_router.include_router(comande_router, prefix="/comande", tags=["comande"])
api_router.include_router(responsabili_router, prefix="/responsabili", tags=["responsabili"])
api_router.include_router(rapporti_generali_router, prefix="/cantieri", tags=["rapporti-generali"])
api_router.include_router(prove_dettagliate_router, prefix="/cantieri", tags=["prove-dettagliate"])
api_router.include_router(non_conformita_router, prefix="/cantieri", tags=["non-conformita"])
api_router.include_router(pdf_cei_64_8_router, prefix="/cantieri", tags=["pdf-cei-64-8"])
api_router.include_router(mobile_router, tags=["mobile"])
api_router.include_router(rapportini_router, prefix="/rapportini", tags=["rapportini"])

# Aggiungi altri router man mano che vengono creati
