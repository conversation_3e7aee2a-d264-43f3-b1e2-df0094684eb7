#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Schemi Pydantic per il database delle tipologie di cavi.
Gestisce validazione e serializzazione per categorie, produttori, standard e tipologie.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


# Schemi per Categorie Cavi
class CategoriaCavoBase(BaseModel):
    """Schema base per le categorie di cavi."""
    nome_categoria: str = Field(..., max_length=100)
    descrizione: Optional[str] = None
    id_categoria_padre: Optional[int] = None
    livello: int = Field(default=1, ge=1, le=5)
    ordine_visualizzazione: int = Field(default=0, ge=0)
    attiva: bool = Field(default=True)


class CategoriaCavoCreate(CategoriaCavoBase):
    """Schema per la creazione di una categoria."""
    pass


class CategoriaCavoUpdate(BaseModel):
    """Schema per l'aggiornamento di una categoria."""
    nome_categoria: Optional[str] = Field(None, max_length=100)
    descrizione: Optional[str] = None
    id_categoria_padre: Optional[int] = None
    livello: Optional[int] = Field(None, ge=1, le=5)
    ordine_visualizzazione: Optional[int] = Field(None, ge=0)
    attiva: Optional[bool] = None


class CategoriaCavoInDB(CategoriaCavoBase):
    """Schema per una categoria nel database."""
    id_categoria: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Schemi per Produttori Cavi
class ProduttoreCavoBase(BaseModel):
    """Schema base per i produttori di cavi."""
    nome_produttore: str = Field(..., max_length=100)
    paese: Optional[str] = Field(None, max_length=50)
    sito_web: Optional[str] = Field(None, max_length=200)
    email_contatto: Optional[str] = Field(None, max_length=100)
    telefono: Optional[str] = Field(None, max_length=50)
    note: Optional[str] = None
    attivo: bool = Field(default=True)


class ProduttoreCavoCreate(ProduttoreCavoBase):
    """Schema per la creazione di un produttore."""
    pass


class ProduttoreCavoUpdate(BaseModel):
    """Schema per l'aggiornamento di un produttore."""
    nome_produttore: Optional[str] = Field(None, max_length=100)
    paese: Optional[str] = Field(None, max_length=50)
    sito_web: Optional[str] = Field(None, max_length=200)
    email_contatto: Optional[str] = Field(None, max_length=100)
    telefono: Optional[str] = Field(None, max_length=50)
    note: Optional[str] = None
    attivo: Optional[bool] = None


class ProduttoreCavoInDB(ProduttoreCavoBase):
    """Schema per un produttore nel database."""
    id_produttore: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Schemi per Standard Cavi
class StandardCavoBase(BaseModel):
    """Schema base per gli standard dei cavi."""
    nome_standard: str = Field(..., max_length=50)
    ente_normativo: Optional[str] = Field(None, max_length=50)
    descrizione: Optional[str] = None
    anno_pubblicazione: Optional[int] = Field(None, ge=1900, le=2100)
    versione: Optional[str] = Field(None, max_length=20)
    url_documento: Optional[str] = Field(None, max_length=300)
    attivo: bool = Field(default=True)


class StandardCavoCreate(StandardCavoBase):
    """Schema per la creazione di uno standard."""
    pass


class StandardCavoUpdate(BaseModel):
    """Schema per l'aggiornamento di uno standard."""
    nome_standard: Optional[str] = Field(None, max_length=50)
    ente_normativo: Optional[str] = Field(None, max_length=50)
    descrizione: Optional[str] = None
    anno_pubblicazione: Optional[int] = Field(None, ge=1900, le=2100)
    versione: Optional[str] = Field(None, max_length=20)
    url_documento: Optional[str] = Field(None, max_length=300)
    attivo: Optional[bool] = None


class StandardCavoInDB(StandardCavoBase):
    """Schema per uno standard nel database."""
    id_standard: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Schemi per Specifiche Tecniche
class SpecificaTecnicaCavoBase(BaseModel):
    """Schema base per le specifiche tecniche."""
    nome_attributo: str = Field(..., max_length=100)
    valore_attributo: str
    unita_misura: Optional[str] = Field(None, max_length=20)
    tipo_dato: str = Field(default='text', regex='^(text|number|boolean|date)$')
    gruppo_attributo: Optional[str] = Field(None, max_length=50)
    ordine_visualizzazione: int = Field(default=0, ge=0)
    obbligatorio: bool = Field(default=False)


class SpecificaTecnicaCavoCreate(SpecificaTecnicaCavoBase):
    """Schema per la creazione di una specifica tecnica."""
    id_tipologia: int


class SpecificaTecnicaCavoUpdate(BaseModel):
    """Schema per l'aggiornamento di una specifica tecnica."""
    nome_attributo: Optional[str] = Field(None, max_length=100)
    valore_attributo: Optional[str] = None
    unita_misura: Optional[str] = Field(None, max_length=20)
    tipo_dato: Optional[str] = Field(None, regex='^(text|number|boolean|date)$')
    gruppo_attributo: Optional[str] = Field(None, max_length=50)
    ordine_visualizzazione: Optional[int] = Field(None, ge=0)
    obbligatorio: Optional[bool] = None


class SpecificaTecnicaCavoInDB(SpecificaTecnicaCavoBase):
    """Schema per una specifica tecnica nel database."""
    id_specifica: int
    id_tipologia: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Schemi per Tipologie Cavi
class TipologiaCavoBase(BaseModel):
    """Schema base per le tipologie di cavi."""
    codice_prodotto: str = Field(..., max_length=100)
    nome_commerciale: Optional[str] = Field(None, max_length=200)
    id_produttore: Optional[int] = None
    id_categoria: int
    id_standard_principale: Optional[int] = None
    descrizione_breve: Optional[str] = None
    descrizione_completa: Optional[str] = None
    materiale_guaina_esterna: Optional[str] = Field(None, max_length=50)
    diametro_esterno_mm: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    peso_kg_per_km: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    temperatura_min_celsius: Optional[int] = Field(None, ge=-273)
    temperatura_max_celsius: Optional[int] = Field(None, le=2000)
    raggio_curvatura_min_mm: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    resistente_uv: bool = Field(default=False)
    resistente_olio: bool = Field(default=False)
    resistente_fiamma: bool = Field(default=False)
    per_esterno: bool = Field(default=False)
    per_interrato: bool = Field(default=False)
    scheda_tecnica_url: Optional[str] = Field(None, max_length=300)
    immagine_url: Optional[str] = Field(None, max_length=300)
    prezzo_indicativo_euro_per_metro: Optional[Decimal] = Field(None, ge=0, decimal_places=4)
    disponibile: bool = Field(default=True)
    note: Optional[str] = None

    @validator('temperatura_max_celsius')
    def validate_temperature_range(cls, v, values):
        if v is not None and 'temperatura_min_celsius' in values and values['temperatura_min_celsius'] is not None:
            if v <= values['temperatura_min_celsius']:
                raise ValueError('La temperatura massima deve essere maggiore di quella minima')
        return v


class TipologiaCavoCreate(TipologiaCavoBase):
    """Schema per la creazione di una tipologia."""
    pass


class TipologiaCavoUpdate(BaseModel):
    """Schema per l'aggiornamento di una tipologia."""
    codice_prodotto: Optional[str] = Field(None, max_length=100)
    nome_commerciale: Optional[str] = Field(None, max_length=200)
    id_produttore: Optional[int] = None
    id_categoria: Optional[int] = None
    id_standard_principale: Optional[int] = None
    descrizione_breve: Optional[str] = None
    descrizione_completa: Optional[str] = None
    materiale_guaina_esterna: Optional[str] = Field(None, max_length=50)
    diametro_esterno_mm: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    peso_kg_per_km: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    temperatura_min_celsius: Optional[int] = Field(None, ge=-273)
    temperatura_max_celsius: Optional[int] = Field(None, le=2000)
    raggio_curvatura_min_mm: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    resistente_uv: Optional[bool] = None
    resistente_olio: Optional[bool] = None
    resistente_fiamma: Optional[bool] = None
    per_esterno: Optional[bool] = None
    per_interrato: Optional[bool] = None
    scheda_tecnica_url: Optional[str] = Field(None, max_length=300)
    immagine_url: Optional[str] = Field(None, max_length=300)
    prezzo_indicativo_euro_per_metro: Optional[Decimal] = Field(None, ge=0, decimal_places=4)
    disponibile: Optional[bool] = None
    note: Optional[str] = None


class TipologiaCavoInDB(TipologiaCavoBase):
    """Schema per una tipologia nel database."""
    id_tipologia: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Schemi complessi con relazioni
class TipologiaCavoCompleta(TipologiaCavoInDB):
    """Schema completo per una tipologia con tutte le relazioni."""
    produttore: Optional[ProduttoreCavoInDB] = None
    categoria: Optional[CategoriaCavoInDB] = None
    standard_principale: Optional[StandardCavoInDB] = None
    specifiche_tecniche: List[SpecificaTecnicaCavoInDB] = []


class CategoriaCavoCompleta(CategoriaCavoInDB):
    """Schema completo per una categoria con sottocategorie."""
    categoria_padre: Optional[CategoriaCavoInDB] = None
    sottocategorie: List[CategoriaCavoInDB] = []
    tipologie: List[TipologiaCavoInDB] = []


# Schemi per ricerca e filtri
class FiltriTipologieCavi(BaseModel):
    """Schema per i filtri di ricerca delle tipologie."""
    categoria_id: Optional[int] = None
    produttore_id: Optional[int] = None
    standard_id: Optional[int] = None
    disponibile: Optional[bool] = None
    per_esterno: Optional[bool] = None
    per_interrato: Optional[bool] = None
    resistente_fiamma: Optional[bool] = None
    search_text: Optional[str] = None
    prezzo_min: Optional[Decimal] = None
    prezzo_max: Optional[Decimal] = None


class RisultatiRicercaTipologie(BaseModel):
    """Schema per i risultati di ricerca."""
    tipologie: List[TipologiaCavoCompleta]
    total_count: int
    page: int
    page_size: int
    total_pages: int
