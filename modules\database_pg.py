#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo per la gestione del database PostgreSQL.
Implementa il pattern Singleton per garantire una sola istanza di connessione al database.
"""

import os
import logging
import threading
from contextlib import contextmanager
from datetime import datetime, date
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class Config:
    """Classe di configurazione centralizzata per il database."""
    # Configurazione del database PostgreSQL
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = os.environ.get('DB_PORT', '5432')
    DB_NAME = os.environ.get('DB_NAME', 'cantieri')
    DB_USER = os.environ.get('DB_USER', 'postgres')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Taranto')

    # Configurazione delle tabelle
    TABELLE_PRINCIPALI = [
        'Utenti', 'Cantieri', 'Cavi', 'parco_cavi', 'CertificazioniCavi', 'StrumentiCertificati',
        'Comande', 'Responsabili', 'RapportiniLavoro',
        'categorie_cavi', 'produttori_cavi', 'standard_cavi', 'tipologie_cavi',
        'specifiche_tecniche_cavi', 'tipologie_standard'
    ]

    # Ruoli utente validi
    RUOLI_UTENTE = ['owner', 'user', 'cantieri_user']

    # Stati delle comande
    STATI_COMANDA = ['CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA', 'ANNULLATA']

    # Tipi di comande
    TIPI_COMANDA = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']

    # Codici di errore
    ERROR_CODES = {
        'duplicate_key': 'Violazione di unicità',
        'foreign_key': 'Violazione di chiave esterna',
        'general_error': 'Errore generico'
    }

    @classmethod
    def get_connection_params(cls):
        """Restituisce i parametri di connessione al database."""
        return {
            'host': cls.DB_HOST,
            'port': cls.DB_PORT,
            'dbname': cls.DB_NAME,
            'user': cls.DB_USER,
            'password': cls.DB_PASSWORD
        }


@contextmanager
def database_connection(autocommit=True, dict_cursor=False, operation_name=None):
    """Context manager migliorato per gestire connessioni al database in modo sicuro.

    Utilizza l'istanza singleton di Database per ottenere una connessione.
    Gestisce transazioni, errori e verifica lo stato del database dopo un errore.

    Args:
        autocommit (bool): Se True, imposta autocommit=True sulla connessione
        dict_cursor (bool): Se True, utilizza RealDictCursor
        operation_name (str): Nome dell'operazione per il logging
    """
    db = Database()
    conn = None
    cursor = None
    operation_desc = f" [{operation_name}]" if operation_name else ""
    transaction_successful = False
    error_occurred = False

    try:
        # Log dell'inizio dell'operazione
        logging.info(f"🔄 Inizio operazione database{operation_desc}")

        # Ottieni la connessione appropriata
        if dict_cursor:
            conn = db.get_dict_cursor_connection()
            # Sovrascrivi l'impostazione autocommit se necessario
            conn.autocommit = autocommit
        else:
            conn = db.get_connection()
            # Sovrascrivi l'impostazione autocommit se necessario
            conn.autocommit = autocommit

        # Crea il cursore
        cursor = conn.cursor()

        # Restituisci connessione e cursore
        yield conn, cursor

        # Se arriviamo qui senza eccezioni e non siamo in autocommit, facciamo commit
        if conn and not autocommit and conn.status == psycopg2.extensions.STATUS_IN_TRANSACTION:
            conn.commit()
            logging.info(f"✅ Commit eseguito con successo{operation_desc}")
            transaction_successful = True

    except psycopg2.Error as e:
        # Segna che si è verificato un errore
        error_occurred = True
        error_code = getattr(e, 'pgcode', None)
        error_message = str(e)

        # In caso di errore, fai rollback se non in autocommit
        if conn and not autocommit:
            try:
                conn.rollback()
                logging.info(f"Rollback eseguito dopo errore{operation_desc}: {error_message}")
            except Exception as rollback_e:
                logging.error(f"❌ Errore durante il rollback{operation_desc}: {str(rollback_e)}")

        # Log dettagliato dell'errore
        if error_code == '23505':  # unique_violation
            logging.error(f"❌ Violazione di unicità durante operazione{operation_desc}: {error_message}")
        elif error_code == '23503':  # foreign_key_violation
            logging.error(f"❌ Violazione di chiave esterna durante operazione{operation_desc}: {error_message}")
        else:
            logging.error(f"❌ Errore database durante operazione{operation_desc}: {error_message}")

        # Rilancia l'eccezione
        raise
    except Exception as e:
        # Gestisci altre eccezioni non specifiche del database
        error_occurred = True
        logging.error(f"❌ Errore generico durante operazione{operation_desc}: {str(e)}")

        # In caso di errore, fai rollback se non in autocommit
        if conn and not autocommit:
            try:
                conn.rollback()
                logging.info(f"Rollback eseguito dopo errore generico{operation_desc}")
            except Exception as rollback_e:
                logging.error(f"❌ Errore durante il rollback dopo errore generico{operation_desc}: {str(rollback_e)}")

        # Rilancia l'eccezione
        raise
    finally:
        # Chiudi il cursore
        if cursor:
            cursor.close()
        # Chiudi la connessione
        if conn:
            # Se non in autocommit e non è stato fatto commit o rollback, fai rollback
            if not autocommit and conn.status == psycopg2.extensions.STATUS_IN_TRANSACTION:
                try:
                    conn.rollback()
                    logging.info(f"Rollback automatico alla chiusura della connessione{operation_desc}")
                except Exception as e:
                    logging.error(f"❌ Errore durante il rollback automatico{operation_desc}: {str(e)}")
            conn.close()

        # Log di completamento dell'operazione
        if not error_occurred:
            logging.info(f"✅ Operazione database completata con successo{operation_desc}")
        else:
            logging.warning(f"⚠️ Operazione database completata con errori{operation_desc}")

# La vecchia versione per compatibilità è stata rimossa perché non più utilizzata


class Database:
    """Classe per la gestione del database con pattern Singleton.
    Garantisce che esista una sola istanza della classe in tutta l'applicazione.
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Implementazione del pattern Singleton thread-safe.

        Returns:
            Database: L'unica istanza della classe Database
        """
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(Database, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Inizializza la connessione al database."""
        # Evita la reinizializzazione se l'istanza è già stata inizializzata
        if getattr(self, '_initialized', False):
            return

        self.conn_params = Config.get_connection_params()
        self._initialized = True
        self.inizializza_database()

    def get_connection(self, autocommit=True):
        """Crea e restituisce una connessione al database.

        Args:
            autocommit (bool): Se True, imposta autocommit=True sulla connessione

        Returns:
            psycopg2.connection: Connessione al database
        """
        # Usa i parametri di connessione dalla configurazione
        conn = psycopg2.connect(**self.conn_params)
        conn.autocommit = autocommit  # Imposta autocommit in base al parametro
        return conn

    def get_dict_cursor_connection(self, autocommit=True):
        """Crea e restituisce una connessione al database con cursor factory RealDictCursor.

        Args:
            autocommit (bool): Se True, imposta autocommit=True sulla connessione

        Returns:
            psycopg2.connection: Connessione al database con cursor factory RealDictCursor
        """
        # Usa i parametri di connessione dalla configurazione
        conn = psycopg2.connect(**self.conn_params)
        conn.autocommit = autocommit  # Imposta autocommit in base al parametro
        conn.cursor_factory = RealDictCursor
        return conn

    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False, description=None):
        """Esegue una query SQL e restituisce opzionalmente i risultati.

        Nota: Si consiglia di utilizzare il context manager database_connection per operazioni più complesse.

        Args:
            query (str): Query SQL da eseguire
            params (tuple, optional): Parametri per la query. Default None.
            fetch_one (bool, optional): Se True, restituisce solo il primo risultato. Default False.
            fetch_all (bool, optional): Se True, restituisce tutti i risultati. Default False.
            description (str, optional): Descrizione della query per il logging. Default None.

        Returns:
            list/tuple/None: Risultati della query o None
        """
        with database_connection(autocommit=True, operation_name=description) as (conn, cursor):
            cursor.execute(query, params)
            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            return None

    # La funzione execute_transaction è stata rimossa perché ridondante rispetto al context manager database_connection

    def inizializza_database(self):
        """Inizializza il database creando le tabelle necessarie se non esistono."""
        try:
            with database_connection(autocommit=True, operation_name="inizializzazione_database") as (conn, cursor):
                # Verifica se le tabelle esistono già
                cursor.execute("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                """)
                existing_tables = [row[0].lower() for row in cursor.fetchall()]

                # Creazione tabella Utenti se non esiste
                if 'utenti' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Utenti (
                        id_utente SERIAL PRIMARY KEY,
                        username TEXT UNIQUE NOT NULL,
                        password TEXT NOT NULL,
                        ruolo TEXT NOT NULL CHECK (ruolo IN ('owner', 'user', 'cantieri_user')),
                        data_scadenza DATE,
                        abilitato BOOLEAN DEFAULT TRUE,
                        created_by INTEGER,
                        password_plain TEXT,
                        telefono VARCHAR(20),
                        cellulare VARCHAR(20),
                        FOREIGN KEY (created_by) REFERENCES Utenti(id_utente)
                    )''')
                    logging.info("✅ Tabella Utenti creata")
                else:
                    # Aggiungi colonne telefono e cellulare se non esistono
                    cursor.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name = 'utenti' AND column_name IN ('telefono', 'cellulare')
                    """)
                    existing_columns = [row[0] for row in cursor.fetchall()]

                    if 'telefono' not in existing_columns:
                        cursor.execute("ALTER TABLE Utenti ADD COLUMN telefono VARCHAR(20)")
                        logging.info("✅ Colonna telefono aggiunta alla tabella Utenti")

                    if 'cellulare' not in existing_columns:
                        cursor.execute("ALTER TABLE Utenti ADD COLUMN cellulare VARCHAR(20)")
                        logging.info("✅ Colonna cellulare aggiunta alla tabella Utenti")

                # Creazione tabella Cantieri se non esiste
                if 'cantieri' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Cantieri (
                        id_cantiere SERIAL PRIMARY KEY,
                        nome TEXT NOT NULL,
                        descrizione TEXT,
                        data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        password_cantiere TEXT NOT NULL,
                        id_utente INTEGER NOT NULL,
                        codice_univoco TEXT UNIQUE NOT NULL,
                        FOREIGN KEY (id_utente) REFERENCES Utenti(id_utente)
                    )''')
                    logging.info("✅ Tabella Cantieri creata")

                # Creazione tabella parco_cavi se non esiste
                if 'parco_cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS parco_cavi (
                        id_bobina TEXT PRIMARY KEY,
                        numero_bobina TEXT NOT NULL,
                        utility TEXT NOT NULL,
                        tipologia TEXT NOT NULL,
                        n_conduttori TEXT NOT NULL,
                        sezione TEXT NOT NULL,
                        metri_totali REAL NOT NULL,
                        metri_residui REAL NOT NULL,
                        stato_bobina TEXT NOT NULL,
                        ubicazione_bobina TEXT,
                        fornitore TEXT,
                        n_ddt TEXT,
                        data_ddt DATE,
                        configurazione TEXT,
                        id_cantiere INTEGER,
                        id_tipologia_cavo INTEGER, -- Collegamento al nuovo database tipologie
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE SET NULL,
                        FOREIGN KEY (id_tipologia_cavo) REFERENCES tipologie_cavi(id_tipologia) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella parco_cavi creata")

                # Creazione tabelle per il database delle tipologie di cavi

                # Tabella categorie cavi (struttura gerarchica)
                if 'categorie_cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS categorie_cavi (
                        id_categoria SERIAL PRIMARY KEY,
                        nome_categoria VARCHAR(100) NOT NULL UNIQUE,
                        descrizione TEXT,
                        id_categoria_padre INTEGER,
                        livello INTEGER NOT NULL DEFAULT 1,
                        ordine_visualizzazione INTEGER DEFAULT 0,
                        attiva BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (id_categoria_padre) REFERENCES categorie_cavi(id_categoria) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella categorie_cavi creata")

                # Tabella produttori
                if 'produttori_cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS produttori_cavi (
                        id_produttore SERIAL PRIMARY KEY,
                        nome_produttore VARCHAR(100) NOT NULL UNIQUE,
                        paese VARCHAR(50),
                        sito_web VARCHAR(200),
                        email_contatto VARCHAR(100),
                        telefono VARCHAR(50),
                        note TEXT,
                        attivo BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )''')
                    logging.info("✅ Tabella produttori_cavi creata")

                # Tabella standard e normative
                if 'standard_cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS standard_cavi (
                        id_standard SERIAL PRIMARY KEY,
                        nome_standard VARCHAR(50) NOT NULL UNIQUE,
                        ente_normativo VARCHAR(50),
                        descrizione TEXT,
                        anno_pubblicazione INTEGER,
                        versione VARCHAR(20),
                        url_documento VARCHAR(300),
                        attivo BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )''')
                    logging.info("✅ Tabella standard_cavi creata")

                # Tabella principale tipologie cavi
                if 'tipologie_cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS tipologie_cavi (
                        id_tipologia SERIAL PRIMARY KEY,
                        codice_prodotto VARCHAR(100) NOT NULL,
                        nome_commerciale VARCHAR(200),
                        id_produttore INTEGER,
                        id_categoria INTEGER NOT NULL,
                        id_standard_principale INTEGER,
                        descrizione_breve TEXT,
                        descrizione_completa TEXT,
                        materiale_guaina_esterna VARCHAR(50),
                        diametro_esterno_mm DECIMAL(8,2),
                        peso_kg_per_km DECIMAL(10,2),
                        temperatura_min_celsius INTEGER,
                        temperatura_max_celsius INTEGER,
                        raggio_curvatura_min_mm DECIMAL(8,2),
                        resistente_uv BOOLEAN DEFAULT FALSE,
                        resistente_olio BOOLEAN DEFAULT FALSE,
                        resistente_fiamma BOOLEAN DEFAULT FALSE,
                        per_esterno BOOLEAN DEFAULT FALSE,
                        per_interrato BOOLEAN DEFAULT FALSE,
                        scheda_tecnica_url VARCHAR(300),
                        immagine_url VARCHAR(300),
                        prezzo_indicativo_euro_per_metro DECIMAL(10,4),
                        disponibile BOOLEAN DEFAULT TRUE,
                        note TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (id_produttore) REFERENCES produttori_cavi(id_produttore) ON DELETE SET NULL,
                        FOREIGN KEY (id_categoria) REFERENCES categorie_cavi(id_categoria) ON DELETE RESTRICT,
                        FOREIGN KEY (id_standard_principale) REFERENCES standard_cavi(id_standard) ON DELETE SET NULL,
                        UNIQUE(codice_prodotto, id_produttore)
                    )''')
                    logging.info("✅ Tabella tipologie_cavi creata")

                # Tabella specifiche tecniche (chiave-valore flessibile)
                if 'specifiche_tecniche_cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS specifiche_tecniche_cavi (
                        id_specifica SERIAL PRIMARY KEY,
                        id_tipologia INTEGER NOT NULL,
                        nome_attributo VARCHAR(100) NOT NULL,
                        valore_attributo TEXT NOT NULL,
                        unita_misura VARCHAR(20),
                        tipo_dato VARCHAR(20) DEFAULT 'text', -- text, number, boolean, date
                        gruppo_attributo VARCHAR(50), -- per raggruppare attributi correlati
                        ordine_visualizzazione INTEGER DEFAULT 0,
                        obbligatorio BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (id_tipologia) REFERENCES tipologie_cavi(id_tipologia) ON DELETE CASCADE,
                        UNIQUE(id_tipologia, nome_attributo)
                    )''')
                    logging.info("✅ Tabella specifiche_tecniche_cavi creata")

                # Tabella relazione tipologie-standard (molti a molti)
                if 'tipologie_standard' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS tipologie_standard (
                        id_tipologia INTEGER NOT NULL,
                        id_standard INTEGER NOT NULL,
                        conforme BOOLEAN DEFAULT TRUE,
                        note_conformita TEXT,
                        data_certificazione DATE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (id_tipologia, id_standard),
                        FOREIGN KEY (id_tipologia) REFERENCES tipologie_cavi(id_tipologia) ON DELETE CASCADE,
                        FOREIGN KEY (id_standard) REFERENCES standard_cavi(id_standard) ON DELETE CASCADE
                    )''')
                    logging.info("✅ Tabella tipologie_standard creata")

                # Creazione tabella Cavi se non esiste
                if 'cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Cavi (
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        utility TEXT NOT NULL,
                        tipologia TEXT NOT NULL,
                        n_conduttori INTEGER NOT NULL,
                        sezione TEXT NOT NULL,
                        metri_teorici REAL NOT NULL,
                        metratura_reale REAL,
                        ubicazione_partenza TEXT NOT NULL,
                        ubicazione_arrivo TEXT NOT NULL,
                        stato_installazione TEXT NOT NULL,
                        data_posa DATE,
                        data_collegamento_partenza DATE,
                        data_collegamento_arrivo DATE,
                        note TEXT,
                        id_bobina TEXT,
                        id_comanda_posa TEXT,
                        id_comanda_collegamento_partenza TEXT,
                        id_comanda_collegamento_arrivo TEXT,
                        revisione_ufficiale TEXT,
                        sistema TEXT,
                        colore_cavo TEXT,
                        sh TEXT,
                        utenza_partenza TEXT,
                        descrizione_utenza_partenza TEXT,
                        utenza_arrivo TEXT,
                        descrizione_utenza_arrivo TEXT,
                        responsabile_posa TEXT,
                        modificato_manualmente INTEGER,
                        collegamenti INTEGER,
                        responsabile_partenza TEXT,
                        responsabile_arrivo TEXT,
                        comanda_posa TEXT,
                        comanda_partenza TEXT,
                        comanda_arrivo TEXT,
                        comanda_certificazione TEXT,
                        stato_certificazione TEXT,
                        data_certificazione_cavo DATE,
                        timestamp TIMESTAMP,
                        id_tipologia_cavo INTEGER, -- Collegamento al nuovo database tipologie
                        PRIMARY KEY (id_cantiere, id_cavo),
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_bobina) REFERENCES parco_cavi(id_bobina) ON DELETE SET NULL,
                        FOREIGN KEY (id_tipologia_cavo) REFERENCES tipologie_cavi(id_tipologia) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella Cavi creata")

                # Aggiorna la tabella Cavi esistente per supportare i campi di certificazione
                else:
                    try:
                        # Aggiungi colonne di certificazione se non esistono
                        columns_to_add = [
                            ('comanda_certificazione', 'TEXT'),
                            ('stato_certificazione', 'TEXT'),
                            ('data_certificazione_cavo', 'DATE')
                        ]

                        for column_name, column_type in columns_to_add:
                            cursor.execute("""
                                SELECT column_name FROM information_schema.columns
                                WHERE table_name = 'cavi' AND column_name = %s
                            """, (column_name,))
                            if not cursor.fetchone():
                                cursor.execute(f"ALTER TABLE Cavi ADD COLUMN {column_name} {column_type}")
                                logging.info(f"✅ Colonna {column_name} aggiunta alla tabella Cavi")

                    except Exception as e:
                        logging.warning(f"⚠️ Errore nell'aggiornamento della tabella Cavi: {str(e)}")

                # Creazione tabella StrumentiCertificati se non esiste
                if 'strumenticertificati' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS StrumentiCertificati (
                        id_strumento SERIAL PRIMARY KEY,
                        nome TEXT,
                        marca TEXT,
                        modello TEXT,
                        numero_serie TEXT,
                        data_taratura DATE,
                        data_scadenza_taratura DATE,
                        certificato_taratura TEXT,
                        note TEXT,
                        id_cantiere INTEGER,
                        timestamp_creazione TIMESTAMP,
                        timestamp_modifica TIMESTAMP,
                        data_calibrazione DATE,
                        data_scadenza_calibrazione DATE,
                        certificato_calibrazione TEXT
                    )''')
                    logging.info("✅ Tabella StrumentiCertificati creata")

                # Creazione tabella Comande se non esiste
                if 'comande' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Comande (
                        id_comanda SERIAL,
                        codice_comanda TEXT UNIQUE NOT NULL,
                        tipo_comanda TEXT NOT NULL CHECK (tipo_comanda IN ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE', 'TESTING')),
                        descrizione TEXT,
                        data_creazione DATE NOT NULL,
                        data_scadenza DATE,
                        data_completamento DATE,
                        responsabile TEXT,
                        stato TEXT NOT NULL CHECK (stato IN ('CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA', 'ANNULLATA')),
                        id_cantiere INTEGER NOT NULL,
                        dettagli_certificazione JSONB,
                        numero_componenti_squadra INTEGER DEFAULT 1,
                        PRIMARY KEY (codice_comanda),
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
                    )''')
                    logging.info("✅ Tabella Comande creata")
                else:
                    # Aggiungi colonna numero_componenti_squadra se non esiste
                    cursor.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name = 'comande' AND column_name = 'numero_componenti_squadra'
                    """)
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE Comande ADD COLUMN numero_componenti_squadra INTEGER DEFAULT 1")
                        logging.info("✅ Aggiunta colonna numero_componenti_squadra alla tabella Comande")

                # Aggiorna la tabella Comande esistente per supportare CERTIFICAZIONE e numero_componenti_squadra
                if 'comande' in existing_tables:
                    try:
                        # Verifica se il constraint esiste e lo aggiorna per includere CERTIFICAZIONE
                        cursor.execute("""
                            SELECT conname FROM pg_constraint
                            WHERE conrelid = 'comande'::regclass
                            AND contype = 'c'
                            AND conname LIKE '%tipo_comanda%'
                        """)
                        constraint_result = cursor.fetchone()

                        if constraint_result:
                            constraint_name = constraint_result[0]
                            cursor.execute(f"ALTER TABLE Comande DROP CONSTRAINT {constraint_name}")
                            logging.info(f"✅ Constraint {constraint_name} rimosso")

                        # Aggiungi il nuovo constraint
                        cursor.execute("""
                            ALTER TABLE Comande ADD CONSTRAINT comande_tipo_comanda_check
                            CHECK (tipo_comanda IN ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE', 'TESTING'))
                        """)
                        logging.info("✅ Constraint tipo_comanda aggiornato per includere CERTIFICAZIONE")

                        # Aggiungi colonna dettagli_certificazione se non esiste
                        cursor.execute("""
                            SELECT column_name FROM information_schema.columns
                            WHERE table_name = 'comande' AND column_name = 'dettagli_certificazione'
                        """)
                        if not cursor.fetchone():
                            cursor.execute("ALTER TABLE Comande ADD COLUMN dettagli_certificazione JSONB")
                            logging.info("✅ Colonna dettagli_certificazione aggiunta alla tabella Comande")

                    except Exception as e:
                        logging.warning(f"⚠️ Errore nell'aggiornamento della tabella Comande: {str(e)}")

                # Creazione tabella CertificazioniCavi se non esiste
                if 'certificazionicavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS CertificazioniCavi (
                        id_certificazione SERIAL PRIMARY KEY,
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        id_strumento INTEGER,
                        data_certificazione DATE,
                        operatore TEXT,
                        valore_continuita TEXT,
                        valore_isolamento TEXT,
                        valore_resistenza TEXT,
                        percorso_certificato TEXT,
                        percorso_foto TEXT,
                        note TEXT,
                        timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        timestamp_modifica TIMESTAMP,
                        numero_certificato TEXT,
                        id_operatore TEXT,
                        strumento_utilizzato TEXT,
                        lunghezza_misurata REAL,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_strumento) REFERENCES StrumentiCertificati(id_strumento) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella CertificazioniCavi creata")

                # Creazione tabella ComandeDettaglio se non esiste
                if 'comandedettaglio' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS ComandeDettaglio (
                        id_dettaglio SERIAL PRIMARY KEY,
                        codice_comanda TEXT NOT NULL,
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        FOREIGN KEY (codice_comanda) REFERENCES Comande(codice_comanda) ON DELETE CASCADE,
                        FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE
                    )''')
                    logging.info("✅ Tabella ComandeDettaglio creata")

                # Creazione tabella Responsabili se non esiste
                if 'responsabili' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Responsabili (
                        id_responsabile SERIAL PRIMARY KEY,
                        nome_responsabile VARCHAR(255) NOT NULL,
                        telefono VARCHAR(20),
                        email VARCHAR(255),
                        id_cantiere INTEGER NOT NULL,
                        data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        attivo BOOLEAN DEFAULT TRUE,
                        CONSTRAINT responsabili_contatto_check
                            CHECK ((email IS NOT NULL AND email != '') OR (telefono IS NOT NULL AND telefono != '')),
                        CONSTRAINT responsabili_email_cantiere_unique UNIQUE (email, id_cantiere),
                        CONSTRAINT responsabili_telefono_cantiere_unique UNIQUE (telefono, id_cantiere),
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
                    )''')
                    logging.info("✅ Tabella Responsabili creata")
                else:
                    # Aggiungi colonna id_cantiere se non esiste
                    cursor.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name = 'responsabili' AND column_name = 'id_cantiere'
                    """)
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE Responsabili ADD COLUMN id_cantiere INTEGER")
                        cursor.execute("ALTER TABLE Responsabili ADD CONSTRAINT responsabili_cantiere_fk FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE")
                        logging.info("✅ Colonna id_cantiere aggiunta alla tabella Responsabili")

                # Creazione tabella RapportiniLavoro se non esiste
                if 'rapportini_lavoro' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS RapportiniLavoro (
                        id_rapportino SERIAL PRIMARY KEY,
                        codice_comanda TEXT NOT NULL,
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        attivita_svolta TEXT NOT NULL CHECK (attivita_svolta IN ('POSATO', 'COLLEGATO', 'TESTATO')),
                        bobina_utilizzata TEXT,
                        metri_posati REAL,
                        ore_lavoro REAL,
                        numero_componenti_squadra INTEGER,
                        note_lavoro TEXT,
                        problemi_riscontrati TEXT,
                        data_lavoro DATE NOT NULL,
                        data_inserimento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        stato_revisione TEXT NOT NULL DEFAULT 'IN_ATTESA' CHECK (stato_revisione IN ('IN_ATTESA', 'APPROVATO', 'RIFIUTATO')),
                        data_revisione TIMESTAMP,
                        note_revisione TEXT,
                        id_utente_revisore INTEGER,
                        metri_posati_corretti REAL,
                        bobina_utilizzata_corretta TEXT,
                        responsabile TEXT NOT NULL,
                        dati_applicati BOOLEAN DEFAULT FALSE,
                        data_applicazione TIMESTAMP,
                        FOREIGN KEY (codice_comanda) REFERENCES Comande(codice_comanda) ON DELETE CASCADE,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_utente_revisore) REFERENCES Utenti(id_utente) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella RapportiniLavoro creata")

                # Creazione indici per ottimizzare le performance del database tipologie cavi
                try:
                    # Indici per categorie_cavi
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_categorie_cavi_padre
                                     ON categorie_cavi(id_categoria_padre)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_categorie_cavi_livello
                                     ON categorie_cavi(livello)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_categorie_cavi_attiva
                                     ON categorie_cavi(attiva)''')

                    # Indici per tipologie_cavi
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_tipologie_cavi_categoria
                                     ON tipologie_cavi(id_categoria)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_tipologie_cavi_produttore
                                     ON tipologie_cavi(id_produttore)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_tipologie_cavi_codice
                                     ON tipologie_cavi(codice_prodotto)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_tipologie_cavi_disponibile
                                     ON tipologie_cavi(disponibile)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_tipologie_cavi_nome
                                     ON tipologie_cavi(nome_commerciale)''')

                    # Indici per specifiche_tecniche_cavi
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_specifiche_tipologia
                                     ON specifiche_tecniche_cavi(id_tipologia)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_specifiche_attributo
                                     ON specifiche_tecniche_cavi(nome_attributo)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_specifiche_gruppo
                                     ON specifiche_tecniche_cavi(gruppo_attributo)''')

                    # Indici per produttori_cavi
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_produttori_cavi_attivo
                                     ON produttori_cavi(attivo)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_produttori_cavi_paese
                                     ON produttori_cavi(paese)''')

                    # Indici per standard_cavi
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_standard_cavi_attivo
                                     ON standard_cavi(attivo)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_standard_cavi_ente
                                     ON standard_cavi(ente_normativo)''')

                    # Indici per cavi e parco_cavi (collegamento al nuovo database)
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_tipologia_cavo
                                     ON cavi(id_tipologia_cavo)''')
                    cursor.execute('''CREATE INDEX IF NOT EXISTS idx_parco_cavi_tipologia_cavo
                                     ON parco_cavi(id_tipologia_cavo)''')

                    logging.info("✅ Indici per database tipologie cavi creati")
                except Exception as e:
                    logging.warning(f"⚠️ Alcuni indici potrebbero già esistere: {e}")

                # Inserimento dati iniziali per il database tipologie cavi
                self._inserisci_dati_iniziali_tipologie_cavi(cursor)

                logging.info("✅ Inizializzazione database completata")

        except psycopg2.Error as e:
            logging.error(f"❌ Errore durante l'inizializzazione del database: {str(e)}")

    def _inserisci_dati_iniziali_tipologie_cavi(self, cursor):
        """Inserisce i dati iniziali per il database delle tipologie di cavi."""
        try:
            # Verifica se ci sono già dati nelle categorie
            cursor.execute("SELECT COUNT(*) FROM categorie_cavi")
            count_categorie = cursor.fetchone()[0]

            if count_categorie == 0:
                # Inserimento categorie principali (livello 1)
                categorie_principali = [
                    ('Cavi di Energia', 'Cavi per il trasporto di corrente elettrica', None, 1, 1),
                    ('Cavi per Comunicazioni e Dati', 'Cavi per trasmissione di segnali e informazioni', None, 1, 2),
                    ('Cavi per Veicoli', 'Cavi progettati per uso in veicoli (auto, aerei, navi)', None, 1, 3),
                    ('Cavi Speciali e Strumentazione', 'Cavi per applicazioni industriali, audio/video, medicali', None, 1, 4)
                ]

                for nome, desc, padre, livello, ordine in categorie_principali:
                    cursor.execute('''INSERT INTO categorie_cavi
                                    (nome_categoria, descrizione, id_categoria_padre, livello, ordine_visualizzazione)
                                    VALUES (%s, %s, %s, %s, %s)''',
                                 (nome, desc, padre, livello, ordine))

                # Recupera gli ID delle categorie principali per le sottocategorie
                cursor.execute("SELECT id_categoria, nome_categoria FROM categorie_cavi WHERE livello = 1")
                categorie_map = {nome: id_cat for id_cat, nome in cursor.fetchall()}

                # Sottocategorie per Cavi di Energia (livello 2)
                energia_id = categorie_map['Cavi di Energia']
                sottocategorie_energia = [
                    ('Alta Tensione (AT)', 'Cavi per tensioni superiori a 35 kV', energia_id, 2, 1),
                    ('Media Tensione (MT)', 'Cavi per tensioni da 1 kV a 35 kV', energia_id, 2, 2),
                    ('Bassa Tensione (BT)', 'Cavi per tensioni fino a 1000V AC / 1500V DC', energia_id, 2, 3),
                    ('Cavi per Energie Rinnovabili', 'Cavi specializzati per impianti solari ed eolici', energia_id, 2, 4)
                ]

                for nome, desc, padre, livello, ordine in sottocategorie_energia:
                    cursor.execute('''INSERT INTO categorie_cavi
                                    (nome_categoria, descrizione, id_categoria_padre, livello, ordine_visualizzazione)
                                    VALUES (%s, %s, %s, %s, %s)''',
                                 (nome, desc, padre, livello, ordine))

                # Sottocategorie per Comunicazioni e Dati (livello 2)
                comm_id = categorie_map['Cavi per Comunicazioni e Dati']
                sottocategorie_comm = [
                    ('Fibra Ottica', 'Cavi in fibra ottica per trasmissione dati ad alta velocità', comm_id, 2, 1),
                    ('Cavi di Rete Ethernet', 'Cavi in rame per reti LAN (Cat 5e, 6, 6A, 7, 8)', comm_id, 2, 2),
                    ('Cavi Coassiali', 'Cavi per segnali TV, SAT, CCTV (RG6, RG59, RG11)', comm_id, 2, 3),
                    ('Cavi Telefonici', 'Cavi per telefonia e telecomunicazioni', comm_id, 2, 4)
                ]

                for nome, desc, padre, livello, ordine in sottocategorie_comm:
                    cursor.execute('''INSERT INTO categorie_cavi
                                    (nome_categoria, descrizione, id_categoria_padre, livello, ordine_visualizzazione)
                                    VALUES (%s, %s, %s, %s, %s)''',
                                 (nome, desc, padre, livello, ordine))

                # Sottocategorie per Veicoli (livello 2)
                veicoli_id = categorie_map['Cavi per Veicoli']
                sottocategorie_veicoli = [
                    ('Cavi Automotive', 'Cavi per automobili (FLRY, FLY)', veicoli_id, 2, 1),
                    ('Cavi Aeronautici', 'Cavi per applicazioni aeronautiche', veicoli_id, 2, 2),
                    ('Cavi Navali', 'Cavi per applicazioni marine e navali', veicoli_id, 2, 3),
                    ('Cavi Ferroviari', 'Cavi per applicazioni ferroviarie', veicoli_id, 2, 4)
                ]

                for nome, desc, padre, livello, ordine in sottocategorie_veicoli:
                    cursor.execute('''INSERT INTO categorie_cavi
                                    (nome_categoria, descrizione, id_categoria_padre, livello, ordine_visualizzazione)
                                    VALUES (%s, %s, %s, %s, %s)''',
                                 (nome, desc, padre, livello, ordine))

                # Sottocategorie per Cavi Speciali (livello 2)
                speciali_id = categorie_map['Cavi Speciali e Strumentazione']
                sottocategorie_speciali = [
                    ('Cavi Audio/Video', 'Cavi per applicazioni audio e video (HDMI, DisplayPort)', speciali_id, 2, 1),
                    ('Cavi di Controllo', 'Cavi per controllo e strumentazione industriale', speciali_id, 2, 2),
                    ('Cavi Medicali', 'Cavi per applicazioni medicali e ospedaliere', speciali_id, 2, 3),
                    ('Cavi Resistenti al Fuoco', 'Cavi con caratteristiche di resistenza al fuoco', speciali_id, 2, 4)
                ]

                for nome, desc, padre, livello, ordine in sottocategorie_speciali:
                    cursor.execute('''INSERT INTO categorie_cavi
                                    (nome_categoria, descrizione, id_categoria_padre, livello, ordine_visualizzazione)
                                    VALUES (%s, %s, %s, %s, %s)''',
                                 (nome, desc, padre, livello, ordine))

                logging.info("✅ Categorie iniziali inserite")

            # Inserimento produttori principali
            cursor.execute("SELECT COUNT(*) FROM produttori_cavi")
            count_produttori = cursor.fetchone()[0]

            if count_produttori == 0:
                produttori_principali = [
                    ('Prysmian Group', 'Italia', 'https://www.prysmiangroup.com', '<EMAIL>', '+39 02 6449 1'),
                    ('Nexans', 'Francia', 'https://www.nexans.com', '<EMAIL>', '+33 1 78 15 00 00'),
                    ('Belden', 'USA', 'https://www.belden.com', '<EMAIL>', '+1 314 854 8000'),
                    ('CommScope', 'USA', 'https://www.commscope.com', '<EMAIL>', '+1 828 324 2200'),
                    ('General Cable', 'USA', 'https://www.generalcable.com', '<EMAIL>', '+1 859 572 8000'),
                    ('Southwire', 'USA', 'https://www.southwire.com', '<EMAIL>', '+1 770 832 4242'),
                    ('Leoni', 'Germania', 'https://www.leoni.com', '<EMAIL>', '+49 911 2023 0'),
                    ('LS Cable & System', 'Corea del Sud', 'https://www.lscns.com', '<EMAIL>', '+82 2 2189 0114'),
                    ('Draka (Prysmian)', 'Paesi Bassi', 'https://www.draka.com', '<EMAIL>', '+31 316 589 111'),
                    ('Furukawa Electric', 'Giappone', 'https://www.furukawa.co.jp', '<EMAIL>', '+81 3 3286 3001')
                ]

                for nome, paese, sito, email, tel in produttori_principali:
                    cursor.execute('''INSERT INTO produttori_cavi
                                    (nome_produttore, paese, sito_web, email_contatto, telefono)
                                    VALUES (%s, %s, %s, %s, %s)''',
                                 (nome, paese, sito, email, tel))

                logging.info("✅ Produttori principali inseriti")

            # Inserimento standard principali
            cursor.execute("SELECT COUNT(*) FROM standard_cavi")
            count_standard = cursor.fetchone()[0]

            if count_standard == 0:
                standard_principali = [
                    ('CEI 20-22', 'CEI', 'Cavi isolati con gomma per tensioni nominali da 1 a 30 kV', 2019, '4.0'),
                    ('CEI 20-13', 'CEI', 'Cavi isolati con PVC per tensioni nominali fino a 450/750 V', 2018, '3.0'),
                    ('CEI 20-35', 'CEI', 'Cavi per energia isolati con gomma non propaganti la fiamma', 2005, '2.0'),
                    ('IEC 60332-1', 'IEC', 'Test di propagazione verticale della fiamma per cavi singoli', 2004, '2.0'),
                    ('EN 50288-2-1', 'CENELEC', 'Cavi metallici multipolari per trasmissione dati digitali', 2003, '1.0'),
                    ('TIA/EIA-568', 'TIA', 'Standard per cablaggio strutturato di telecomunicazioni', 2020, 'C.2'),
                    ('ISO/IEC 11801', 'ISO/IEC', 'Cablaggio generico per locali del cliente', 2017, '3.0'),
                    ('IEC 60794-2', 'IEC', 'Cavi in fibra ottica - Specifiche di prodotto per cavi interni', 2019, '4.0'),
                    ('CPR EN 50575', 'CENELEC', 'Regolamento Prodotti da Costruzione per cavi', 2014, '1.0'),
                    ('IEC 60228', 'IEC', 'Conduttori di cavi isolati', 2004, '3.0'),
                    ('HD 21.1', 'CENELEC', 'Cavi di distribuzione con isolamento in PVC 450/750V', 2007, '2.0'),
                    ('EN 50267', 'CENELEC', 'Metodi di prova comuni per cavi sottoposti al fuoco', 2009, '2.1')
                ]

                for nome, ente, desc, anno, vers in standard_principali:
                    cursor.execute('''INSERT INTO standard_cavi
                                    (nome_standard, ente_normativo, descrizione, anno_pubblicazione, versione)
                                    VALUES (%s, %s, %s, %s, %s)''',
                                 (nome, ente, desc, anno, vers))

                logging.info("✅ Standard principali inseriti")

            logging.info("✅ Database tipologie cavi inizializzato con successo")

        except Exception as e:
            logging.error(f"❌ Errore nell'inserimento dati iniziali tipologie cavi: {e}")

    def verifica_integrita_database(self):
        """Verifica l'integrità del database controllando la presenza di tutte le tabelle necessarie.

        Returns:
            bool: True se il database è integro, False altrimenti
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Verifica se le tabelle esistono
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public'
                    """)
                    existing_tables = [row[0].lower() for row in cursor.fetchall()]

                    # Verifica che tutte le tabelle principali esistano
                    tabelle_mancanti = []
                    for tabella in [t.lower() for t in Config.TABELLE_PRINCIPALI]:
                        if tabella not in existing_tables:
                            tabelle_mancanti.append(tabella)

                    if tabelle_mancanti:
                        for tabella in tabelle_mancanti:
                            logging.warning(f"⚠️ Tabella mancante: {tabella}")
                        return False

                    logging.info("✅ Database integro")
                    return True

        except psycopg2.Error as e:
            logging.error(f"❌ Errore durante la verifica del database: {str(e)}")
            return False

    def reset_database(self) -> bool:
        """Resetta completamente il database eliminando tutti i dati.

        Returns:
            bool: True se il reset è avvenuto con successo, False altrimenti
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Ottieni tutte le tabelle dal database
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public'
                    """)
                    tabelle = [row[0] for row in cursor.fetchall()]

                    # Disabilita temporaneamente i vincoli di foreign key
                    cursor.execute("SET session_replication_role = 'replica';")

                    # Elimina i dati da tutte le tabelle
                    for tabella in tabelle:
                        if tabella.lower() not in ['pg_stat_statements']:  # Ignora tabelle di sistema
                            cursor.execute(sql.SQL("TRUNCATE TABLE {} CASCADE").format(sql.Identifier(tabella)))
                            logging.info(f"✅ Dati eliminati dalla tabella {tabella}")

                    # Riabilita i vincoli di foreign key
                    cursor.execute("SET session_replication_role = 'origin';")

                    logging.info("✅ Reset database completato con successo")
                    return True

        except psycopg2.Error as e:
            logging.error(f"❌ Errore durante il reset del database: {str(e)}")
            return False

    # La funzione visualizza_database_raw è stata rimossa perché utilizzata solo per debug

    def handle_db_error(self, operation: str, error: Exception) -> str:
        """
        Gestisce gli errori del database in modo centralizzato.

        Args:
            operation: Descrizione dell'operazione che ha generato l'errore
            error: L'eccezione catturata

        Returns:
            str: Codice di errore standardizzato
        """
        error_code = getattr(error, 'pgcode', None)
        error_message = str(error)

        if error_code == '23505':  # unique_violation
            logging.error(f"❌ Violazione di unicità durante {operation}: {error_message}")
            return "duplicate_key"
        elif error_code == '23503':  # foreign_key_violation
            logging.error(f"❌ Violazione di chiave esterna durante {operation}: {error_message}")
            return "foreign_key"
        else:
            logging.error(f"❌ Errore generico durante {operation}: {error_message}")
            return "general_error"

    def verifica_stato_dopo_errore(self, tabella: str, condizioni: dict, campi_da_verificare: dict = None, descrizione: str = None) -> dict:
        """
        Verifica lo stato del database dopo un errore per determinare se l'operazione è stata comunque completata.
        Può anche verificare che specifici campi abbiano valori attesi.

        Args:
            tabella: Nome della tabella da verificare
            condizioni: Dizionario con le condizioni di ricerca (campo: valore)
            campi_da_verificare: Dizionario con i campi da verificare e i loro valori attesi (campo: valore)
            descrizione: Descrizione dell'operazione per il logging

        Returns:
            dict: Dizionario con 'esiste' (bool) e 'valori_corretti' (bool)
        """
        desc = f" ({descrizione})" if descrizione else ""
        result = {'esiste': False, 'valori_corretti': False, 'record': None}

        try:
            # Costruisci la query di verifica
            if campi_da_verificare:
                # Se ci sono campi da verificare, seleziona quei campi specifici
                select_fields = ", ".join(list(campi_da_verificare.keys()))
                query = f"SELECT {select_fields} FROM {tabella} WHERE "
            else:
                # Altrimenti, conta solo i record
                query = f"SELECT COUNT(*) FROM {tabella} WHERE "

            # Costruisci le condizioni WHERE
            conditions = [f"{campo} = %s" for campo in condizioni.keys()]
            params = list(condizioni.values())
            query += " AND ".join(conditions)

            # Esegui la query
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, params)

                    if campi_da_verificare:
                        # Verifica campi specifici
                        record = cursor.fetchone()
                        if not record:
                            logging.info(f"⚠️ Verifica post-errore{desc}: record non trovato")
                            return result

                        result['esiste'] = True
                        result['record'] = record

                        # Verifica i valori dei campi
                        valori_corretti = True
                        for campo, valore_atteso in campi_da_verificare.items():
                            # Estrai il valore effettivo (gestendo sia dict che tuple)
                            valore_effettivo = record[campo] if isinstance(record, dict) else record[0]

                            # Confronta i valori (convertendo in stringhe per sicurezza)
                            if str(valore_effettivo) != str(valore_atteso):
                                valori_corretti = False
                                logging.warning(f"⚠️ Campo {campo} ha valore {valore_effettivo} invece di {valore_atteso}")
                                break

                        result['valori_corretti'] = valori_corretti

                        if valori_corretti:
                            logging.info(f"✅ Verifica post-errore{desc}: operazione completata con successo e valori corretti")
                        else:
                            logging.warning(f"⚠️ Verifica post-errore{desc}: record trovato ma valori non corretti")
                    else:
                        # Verifica solo l'esistenza del record
                        count = cursor.fetchone()[0]
                        result['esiste'] = count > 0

                        if result['esiste']:
                            logging.info(f"✅ Verifica post-errore{desc}: operazione completata con successo nonostante l'errore")
                        else:
                            logging.info(f"⚠️ Verifica post-errore{desc}: operazione non completata")

            return result

        except Exception as e:
            logging.error(f"❌ Errore durante la verifica post-errore{desc}: {str(e)}")
            return result
