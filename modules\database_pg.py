#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo per la gestione del database PostgreSQL.
Implementa il pattern Singleton per garantire una sola istanza di connessione al database.
"""

import os
import logging
import threading
from contextlib import contextmanager
from datetime import datetime, date
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class Config:
    """Classe di configurazione centralizzata per il database."""
    # Configurazione del database PostgreSQL
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = os.environ.get('DB_PORT', '5432')
    DB_NAME = os.environ.get('DB_NAME', 'cantieri')
    DB_USER = os.environ.get('DB_USER', 'postgres')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Taranto')

    # Configurazione delle tabelle
    TABELLE_PRINCIPALI = ['Utenti', 'Cantieri', 'Cavi', 'parco_cavi', 'CertificazioniCavi', 'StrumentiCertificati', 'Comande', 'Responsabili', 'RapportiniLavoro']

    # Ruoli utente validi
    RUOLI_UTENTE = ['owner', 'user', 'cantieri_user']

    # Stati delle comande
    STATI_COMANDA = ['CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA', 'ANNULLATA']

    # Tipi di comande
    TIPI_COMANDA = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']

    # Codici di errore
    ERROR_CODES = {
        'duplicate_key': 'Violazione di unicità',
        'foreign_key': 'Violazione di chiave esterna',
        'general_error': 'Errore generico'
    }

    @classmethod
    def get_connection_params(cls):
        """Restituisce i parametri di connessione al database."""
        return {
            'host': cls.DB_HOST,
            'port': cls.DB_PORT,
            'dbname': cls.DB_NAME,
            'user': cls.DB_USER,
            'password': cls.DB_PASSWORD
        }


@contextmanager
def database_connection(autocommit=True, dict_cursor=False, operation_name=None):
    """Context manager migliorato per gestire connessioni al database in modo sicuro.

    Utilizza l'istanza singleton di Database per ottenere una connessione.
    Gestisce transazioni, errori e verifica lo stato del database dopo un errore.

    Args:
        autocommit (bool): Se True, imposta autocommit=True sulla connessione
        dict_cursor (bool): Se True, utilizza RealDictCursor
        operation_name (str): Nome dell'operazione per il logging
    """
    db = Database()
    conn = None
    cursor = None
    operation_desc = f" [{operation_name}]" if operation_name else ""
    transaction_successful = False
    error_occurred = False

    try:
        # Log dell'inizio dell'operazione
        logging.info(f"🔄 Inizio operazione database{operation_desc}")

        # Ottieni la connessione appropriata
        if dict_cursor:
            conn = db.get_dict_cursor_connection()
            # Sovrascrivi l'impostazione autocommit se necessario
            conn.autocommit = autocommit
        else:
            conn = db.get_connection()
            # Sovrascrivi l'impostazione autocommit se necessario
            conn.autocommit = autocommit

        # Crea il cursore
        cursor = conn.cursor()

        # Restituisci connessione e cursore
        yield conn, cursor

        # Se arriviamo qui senza eccezioni e non siamo in autocommit, facciamo commit
        if conn and not autocommit and conn.status == psycopg2.extensions.STATUS_IN_TRANSACTION:
            conn.commit()
            logging.info(f"✅ Commit eseguito con successo{operation_desc}")
            transaction_successful = True

    except psycopg2.Error as e:
        # Segna che si è verificato un errore
        error_occurred = True
        error_code = getattr(e, 'pgcode', None)
        error_message = str(e)

        # In caso di errore, fai rollback se non in autocommit
        if conn and not autocommit:
            try:
                conn.rollback()
                logging.info(f"Rollback eseguito dopo errore{operation_desc}: {error_message}")
            except Exception as rollback_e:
                logging.error(f"❌ Errore durante il rollback{operation_desc}: {str(rollback_e)}")

        # Log dettagliato dell'errore
        if error_code == '23505':  # unique_violation
            logging.error(f"❌ Violazione di unicità durante operazione{operation_desc}: {error_message}")
        elif error_code == '23503':  # foreign_key_violation
            logging.error(f"❌ Violazione di chiave esterna durante operazione{operation_desc}: {error_message}")
        else:
            logging.error(f"❌ Errore database durante operazione{operation_desc}: {error_message}")

        # Rilancia l'eccezione
        raise
    except Exception as e:
        # Gestisci altre eccezioni non specifiche del database
        error_occurred = True
        logging.error(f"❌ Errore generico durante operazione{operation_desc}: {str(e)}")

        # In caso di errore, fai rollback se non in autocommit
        if conn and not autocommit:
            try:
                conn.rollback()
                logging.info(f"Rollback eseguito dopo errore generico{operation_desc}")
            except Exception as rollback_e:
                logging.error(f"❌ Errore durante il rollback dopo errore generico{operation_desc}: {str(rollback_e)}")

        # Rilancia l'eccezione
        raise
    finally:
        # Chiudi il cursore
        if cursor:
            cursor.close()
        # Chiudi la connessione
        if conn:
            # Se non in autocommit e non è stato fatto commit o rollback, fai rollback
            if not autocommit and conn.status == psycopg2.extensions.STATUS_IN_TRANSACTION:
                try:
                    conn.rollback()
                    logging.info(f"Rollback automatico alla chiusura della connessione{operation_desc}")
                except Exception as e:
                    logging.error(f"❌ Errore durante il rollback automatico{operation_desc}: {str(e)}")
            conn.close()

        # Log di completamento dell'operazione
        if not error_occurred:
            logging.info(f"✅ Operazione database completata con successo{operation_desc}")
        else:
            logging.warning(f"⚠️ Operazione database completata con errori{operation_desc}")

# La vecchia versione per compatibilità è stata rimossa perché non più utilizzata


class Database:
    """Classe per la gestione del database con pattern Singleton.
    Garantisce che esista una sola istanza della classe in tutta l'applicazione.
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Implementazione del pattern Singleton thread-safe.

        Returns:
            Database: L'unica istanza della classe Database
        """
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(Database, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Inizializza la connessione al database."""
        # Evita la reinizializzazione se l'istanza è già stata inizializzata
        if getattr(self, '_initialized', False):
            return

        self.conn_params = Config.get_connection_params()
        self._initialized = True
        self.inizializza_database()

    def get_connection(self, autocommit=True):
        """Crea e restituisce una connessione al database.

        Args:
            autocommit (bool): Se True, imposta autocommit=True sulla connessione

        Returns:
            psycopg2.connection: Connessione al database
        """
        # Usa i parametri di connessione dalla configurazione
        conn = psycopg2.connect(**self.conn_params)
        conn.autocommit = autocommit  # Imposta autocommit in base al parametro
        return conn

    def get_dict_cursor_connection(self, autocommit=True):
        """Crea e restituisce una connessione al database con cursor factory RealDictCursor.

        Args:
            autocommit (bool): Se True, imposta autocommit=True sulla connessione

        Returns:
            psycopg2.connection: Connessione al database con cursor factory RealDictCursor
        """
        # Usa i parametri di connessione dalla configurazione
        conn = psycopg2.connect(**self.conn_params)
        conn.autocommit = autocommit  # Imposta autocommit in base al parametro
        conn.cursor_factory = RealDictCursor
        return conn

    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False, description=None):
        """Esegue una query SQL e restituisce opzionalmente i risultati.

        Nota: Si consiglia di utilizzare il context manager database_connection per operazioni più complesse.

        Args:
            query (str): Query SQL da eseguire
            params (tuple, optional): Parametri per la query. Default None.
            fetch_one (bool, optional): Se True, restituisce solo il primo risultato. Default False.
            fetch_all (bool, optional): Se True, restituisce tutti i risultati. Default False.
            description (str, optional): Descrizione della query per il logging. Default None.

        Returns:
            list/tuple/None: Risultati della query o None
        """
        with database_connection(autocommit=True, operation_name=description) as (conn, cursor):
            cursor.execute(query, params)
            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            return None

    # La funzione execute_transaction è stata rimossa perché ridondante rispetto al context manager database_connection

    def inizializza_database(self):
        """Inizializza il database creando le tabelle necessarie se non esistono."""
        try:
            with database_connection(autocommit=True, operation_name="inizializzazione_database") as (conn, cursor):
                # Verifica se le tabelle esistono già
                cursor.execute("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                """)
                existing_tables = [row[0].lower() for row in cursor.fetchall()]

                # Creazione tabella Utenti se non esiste
                if 'utenti' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Utenti (
                        id_utente SERIAL PRIMARY KEY,
                        username TEXT UNIQUE NOT NULL,
                        password TEXT NOT NULL,
                        ruolo TEXT NOT NULL CHECK (ruolo IN ('owner', 'user', 'cantieri_user')),
                        data_scadenza DATE,
                        abilitato BOOLEAN DEFAULT TRUE,
                        created_by INTEGER,
                        password_plain TEXT,
                        telefono VARCHAR(20),
                        cellulare VARCHAR(20),
                        FOREIGN KEY (created_by) REFERENCES Utenti(id_utente)
                    )''')
                    logging.info("✅ Tabella Utenti creata")
                else:
                    # Aggiungi colonne telefono e cellulare se non esistono
                    cursor.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name = 'utenti' AND column_name IN ('telefono', 'cellulare')
                    """)
                    existing_columns = [row[0] for row in cursor.fetchall()]

                    if 'telefono' not in existing_columns:
                        cursor.execute("ALTER TABLE Utenti ADD COLUMN telefono VARCHAR(20)")
                        logging.info("✅ Colonna telefono aggiunta alla tabella Utenti")

                    if 'cellulare' not in existing_columns:
                        cursor.execute("ALTER TABLE Utenti ADD COLUMN cellulare VARCHAR(20)")
                        logging.info("✅ Colonna cellulare aggiunta alla tabella Utenti")

                # Creazione tabella Cantieri se non esiste
                if 'cantieri' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Cantieri (
                        id_cantiere SERIAL PRIMARY KEY,
                        nome TEXT NOT NULL,
                        descrizione TEXT,
                        data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        password_cantiere TEXT NOT NULL,
                        id_utente INTEGER NOT NULL,
                        codice_univoco TEXT UNIQUE NOT NULL,
                        FOREIGN KEY (id_utente) REFERENCES Utenti(id_utente)
                    )''')
                    logging.info("✅ Tabella Cantieri creata")

                # Creazione tabella parco_cavi se non esiste
                if 'parco_cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS parco_cavi (
                        id_bobina TEXT PRIMARY KEY,
                        numero_bobina TEXT NOT NULL,
                        utility TEXT NOT NULL,
                        tipologia TEXT NOT NULL,
                        n_conduttori TEXT NOT NULL,
                        sezione TEXT NOT NULL,
                        metri_totali REAL NOT NULL,
                        metri_residui REAL NOT NULL,
                        stato_bobina TEXT NOT NULL,
                        ubicazione_bobina TEXT,
                        fornitore TEXT,
                        n_ddt TEXT,
                        data_ddt DATE,
                        configurazione TEXT,
                        id_cantiere INTEGER,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella parco_cavi creata")

                # Creazione tabella Cavi se non esiste
                if 'cavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Cavi (
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        utility TEXT NOT NULL,
                        tipologia TEXT NOT NULL,
                        n_conduttori INTEGER NOT NULL,
                        sezione TEXT NOT NULL,
                        metri_teorici REAL NOT NULL,
                        metratura_reale REAL,
                        ubicazione_partenza TEXT NOT NULL,
                        ubicazione_arrivo TEXT NOT NULL,
                        stato_installazione TEXT NOT NULL,
                        data_posa DATE,
                        data_collegamento_partenza DATE,
                        data_collegamento_arrivo DATE,
                        note TEXT,
                        id_bobina TEXT,
                        id_comanda_posa TEXT,
                        id_comanda_collegamento_partenza TEXT,
                        id_comanda_collegamento_arrivo TEXT,
                        revisione_ufficiale TEXT,
                        sistema TEXT,
                        colore_cavo TEXT,
                        sh TEXT,
                        utenza_partenza TEXT,
                        descrizione_utenza_partenza TEXT,
                        utenza_arrivo TEXT,
                        descrizione_utenza_arrivo TEXT,
                        responsabile_posa TEXT,
                        modificato_manualmente INTEGER,
                        collegamenti INTEGER,
                        responsabile_partenza TEXT,
                        responsabile_arrivo TEXT,
                        comanda_posa TEXT,
                        comanda_partenza TEXT,
                        comanda_arrivo TEXT,
                        comanda_certificazione TEXT,
                        stato_certificazione TEXT,
                        data_certificazione_cavo DATE,
                        timestamp TIMESTAMP,
                        PRIMARY KEY (id_cantiere, id_cavo),
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_bobina) REFERENCES parco_cavi(id_bobina) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella Cavi creata")

                # Aggiorna la tabella Cavi esistente per supportare i campi di certificazione
                else:
                    try:
                        # Aggiungi colonne di certificazione se non esistono
                        columns_to_add = [
                            ('comanda_certificazione', 'TEXT'),
                            ('stato_certificazione', 'TEXT'),
                            ('data_certificazione_cavo', 'DATE')
                        ]

                        for column_name, column_type in columns_to_add:
                            cursor.execute("""
                                SELECT column_name FROM information_schema.columns
                                WHERE table_name = 'cavi' AND column_name = %s
                            """, (column_name,))
                            if not cursor.fetchone():
                                cursor.execute(f"ALTER TABLE Cavi ADD COLUMN {column_name} {column_type}")
                                logging.info(f"✅ Colonna {column_name} aggiunta alla tabella Cavi")

                    except Exception as e:
                        logging.warning(f"⚠️ Errore nell'aggiornamento della tabella Cavi: {str(e)}")

                # Creazione tabella StrumentiCertificati se non esiste
                if 'strumenticertificati' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS StrumentiCertificati (
                        id_strumento SERIAL PRIMARY KEY,
                        nome TEXT,
                        marca TEXT,
                        modello TEXT,
                        numero_serie TEXT,
                        data_taratura DATE,
                        data_scadenza_taratura DATE,
                        certificato_taratura TEXT,
                        note TEXT,
                        id_cantiere INTEGER,
                        timestamp_creazione TIMESTAMP,
                        timestamp_modifica TIMESTAMP,
                        data_calibrazione DATE,
                        data_scadenza_calibrazione DATE,
                        certificato_calibrazione TEXT
                    )''')
                    logging.info("✅ Tabella StrumentiCertificati creata")

                # Creazione tabella Comande se non esiste
                if 'comande' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Comande (
                        id_comanda SERIAL,
                        codice_comanda TEXT UNIQUE NOT NULL,
                        tipo_comanda TEXT NOT NULL CHECK (tipo_comanda IN ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE', 'TESTING')),
                        descrizione TEXT,
                        data_creazione DATE NOT NULL,
                        data_scadenza DATE,
                        data_completamento DATE,
                        responsabile TEXT,
                        stato TEXT NOT NULL CHECK (stato IN ('CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA', 'ANNULLATA')),
                        id_cantiere INTEGER NOT NULL,
                        dettagli_certificazione JSONB,
                        numero_componenti_squadra INTEGER DEFAULT 1,
                        PRIMARY KEY (codice_comanda),
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
                    )''')
                    logging.info("✅ Tabella Comande creata")
                else:
                    # Aggiungi colonna numero_componenti_squadra se non esiste
                    cursor.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name = 'comande' AND column_name = 'numero_componenti_squadra'
                    """)
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE Comande ADD COLUMN numero_componenti_squadra INTEGER DEFAULT 1")
                        logging.info("✅ Aggiunta colonna numero_componenti_squadra alla tabella Comande")

                # Aggiorna la tabella Comande esistente per supportare CERTIFICAZIONE e numero_componenti_squadra
                if 'comande' in existing_tables:
                    try:
                        # Verifica se il constraint esiste e lo aggiorna per includere CERTIFICAZIONE
                        cursor.execute("""
                            SELECT conname FROM pg_constraint
                            WHERE conrelid = 'comande'::regclass
                            AND contype = 'c'
                            AND conname LIKE '%tipo_comanda%'
                        """)
                        constraint_result = cursor.fetchone()

                        if constraint_result:
                            constraint_name = constraint_result[0]
                            cursor.execute(f"ALTER TABLE Comande DROP CONSTRAINT {constraint_name}")
                            logging.info(f"✅ Constraint {constraint_name} rimosso")

                        # Aggiungi il nuovo constraint
                        cursor.execute("""
                            ALTER TABLE Comande ADD CONSTRAINT comande_tipo_comanda_check
                            CHECK (tipo_comanda IN ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE', 'TESTING'))
                        """)
                        logging.info("✅ Constraint tipo_comanda aggiornato per includere CERTIFICAZIONE")

                        # Aggiungi colonna dettagli_certificazione se non esiste
                        cursor.execute("""
                            SELECT column_name FROM information_schema.columns
                            WHERE table_name = 'comande' AND column_name = 'dettagli_certificazione'
                        """)
                        if not cursor.fetchone():
                            cursor.execute("ALTER TABLE Comande ADD COLUMN dettagli_certificazione JSONB")
                            logging.info("✅ Colonna dettagli_certificazione aggiunta alla tabella Comande")

                    except Exception as e:
                        logging.warning(f"⚠️ Errore nell'aggiornamento della tabella Comande: {str(e)}")

                # Creazione tabella CertificazioniCavi se non esiste
                if 'certificazionicavi' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS CertificazioniCavi (
                        id_certificazione SERIAL PRIMARY KEY,
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        id_strumento INTEGER,
                        data_certificazione DATE,
                        operatore TEXT,
                        valore_continuita TEXT,
                        valore_isolamento TEXT,
                        valore_resistenza TEXT,
                        percorso_certificato TEXT,
                        percorso_foto TEXT,
                        note TEXT,
                        timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        timestamp_modifica TIMESTAMP,
                        numero_certificato TEXT,
                        id_operatore TEXT,
                        strumento_utilizzato TEXT,
                        lunghezza_misurata REAL,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_strumento) REFERENCES StrumentiCertificati(id_strumento) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella CertificazioniCavi creata")

                # Creazione tabella ComandeDettaglio se non esiste
                if 'comandedettaglio' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS ComandeDettaglio (
                        id_dettaglio SERIAL PRIMARY KEY,
                        codice_comanda TEXT NOT NULL,
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        FOREIGN KEY (codice_comanda) REFERENCES Comande(codice_comanda) ON DELETE CASCADE,
                        FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE
                    )''')
                    logging.info("✅ Tabella ComandeDettaglio creata")

                # Creazione tabella Responsabili se non esiste
                if 'responsabili' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS Responsabili (
                        id_responsabile SERIAL PRIMARY KEY,
                        nome_responsabile VARCHAR(255) NOT NULL,
                        telefono VARCHAR(20),
                        email VARCHAR(255),
                        id_cantiere INTEGER NOT NULL,
                        data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        attivo BOOLEAN DEFAULT TRUE,
                        CONSTRAINT responsabili_contatto_check
                            CHECK ((email IS NOT NULL AND email != '') OR (telefono IS NOT NULL AND telefono != '')),
                        CONSTRAINT responsabili_email_cantiere_unique UNIQUE (email, id_cantiere),
                        CONSTRAINT responsabili_telefono_cantiere_unique UNIQUE (telefono, id_cantiere),
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
                    )''')
                    logging.info("✅ Tabella Responsabili creata")
                else:
                    # Aggiungi colonna id_cantiere se non esiste
                    cursor.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name = 'responsabili' AND column_name = 'id_cantiere'
                    """)
                    if not cursor.fetchone():
                        cursor.execute("ALTER TABLE Responsabili ADD COLUMN id_cantiere INTEGER")
                        cursor.execute("ALTER TABLE Responsabili ADD CONSTRAINT responsabili_cantiere_fk FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE")
                        logging.info("✅ Colonna id_cantiere aggiunta alla tabella Responsabili")

                # Creazione tabella RapportiniLavoro se non esiste
                if 'rapportini_lavoro' not in existing_tables:
                    cursor.execute('''CREATE TABLE IF NOT EXISTS RapportiniLavoro (
                        id_rapportino SERIAL PRIMARY KEY,
                        codice_comanda TEXT NOT NULL,
                        id_cavo TEXT NOT NULL,
                        id_cantiere INTEGER NOT NULL,
                        attivita_svolta TEXT NOT NULL CHECK (attivita_svolta IN ('POSATO', 'COLLEGATO', 'TESTATO')),
                        bobina_utilizzata TEXT,
                        metri_posati REAL,
                        ore_lavoro REAL,
                        numero_componenti_squadra INTEGER,
                        note_lavoro TEXT,
                        problemi_riscontrati TEXT,
                        data_lavoro DATE NOT NULL,
                        data_inserimento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        stato_revisione TEXT NOT NULL DEFAULT 'IN_ATTESA' CHECK (stato_revisione IN ('IN_ATTESA', 'APPROVATO', 'RIFIUTATO')),
                        data_revisione TIMESTAMP,
                        note_revisione TEXT,
                        id_utente_revisore INTEGER,
                        metri_posati_corretti REAL,
                        bobina_utilizzata_corretta TEXT,
                        responsabile TEXT NOT NULL,
                        dati_applicati BOOLEAN DEFAULT FALSE,
                        data_applicazione TIMESTAMP,
                        FOREIGN KEY (codice_comanda) REFERENCES Comande(codice_comanda) ON DELETE CASCADE,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_utente_revisore) REFERENCES Utenti(id_utente) ON DELETE SET NULL
                    )''')
                    logging.info("✅ Tabella RapportiniLavoro creata")

                logging.info("✅ Inizializzazione database completata")

        except psycopg2.Error as e:
            logging.error(f"❌ Errore durante l'inizializzazione del database: {str(e)}")

    def verifica_integrita_database(self):
        """Verifica l'integrità del database controllando la presenza di tutte le tabelle necessarie.

        Returns:
            bool: True se il database è integro, False altrimenti
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Verifica se le tabelle esistono
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public'
                    """)
                    existing_tables = [row[0].lower() for row in cursor.fetchall()]

                    # Verifica che tutte le tabelle principali esistano
                    tabelle_mancanti = []
                    for tabella in [t.lower() for t in Config.TABELLE_PRINCIPALI]:
                        if tabella not in existing_tables:
                            tabelle_mancanti.append(tabella)

                    if tabelle_mancanti:
                        for tabella in tabelle_mancanti:
                            logging.warning(f"⚠️ Tabella mancante: {tabella}")
                        return False

                    logging.info("✅ Database integro")
                    return True

        except psycopg2.Error as e:
            logging.error(f"❌ Errore durante la verifica del database: {str(e)}")
            return False

    def reset_database(self) -> bool:
        """Resetta completamente il database eliminando tutti i dati.

        Returns:
            bool: True se il reset è avvenuto con successo, False altrimenti
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Ottieni tutte le tabelle dal database
                    cursor.execute("""
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public'
                    """)
                    tabelle = [row[0] for row in cursor.fetchall()]

                    # Disabilita temporaneamente i vincoli di foreign key
                    cursor.execute("SET session_replication_role = 'replica';")

                    # Elimina i dati da tutte le tabelle
                    for tabella in tabelle:
                        if tabella.lower() not in ['pg_stat_statements']:  # Ignora tabelle di sistema
                            cursor.execute(sql.SQL("TRUNCATE TABLE {} CASCADE").format(sql.Identifier(tabella)))
                            logging.info(f"✅ Dati eliminati dalla tabella {tabella}")

                    # Riabilita i vincoli di foreign key
                    cursor.execute("SET session_replication_role = 'origin';")

                    logging.info("✅ Reset database completato con successo")
                    return True

        except psycopg2.Error as e:
            logging.error(f"❌ Errore durante il reset del database: {str(e)}")
            return False

    # La funzione visualizza_database_raw è stata rimossa perché utilizzata solo per debug

    def handle_db_error(self, operation: str, error: Exception) -> str:
        """
        Gestisce gli errori del database in modo centralizzato.

        Args:
            operation: Descrizione dell'operazione che ha generato l'errore
            error: L'eccezione catturata

        Returns:
            str: Codice di errore standardizzato
        """
        error_code = getattr(error, 'pgcode', None)
        error_message = str(error)

        if error_code == '23505':  # unique_violation
            logging.error(f"❌ Violazione di unicità durante {operation}: {error_message}")
            return "duplicate_key"
        elif error_code == '23503':  # foreign_key_violation
            logging.error(f"❌ Violazione di chiave esterna durante {operation}: {error_message}")
            return "foreign_key"
        else:
            logging.error(f"❌ Errore generico durante {operation}: {error_message}")
            return "general_error"

    def verifica_stato_dopo_errore(self, tabella: str, condizioni: dict, campi_da_verificare: dict = None, descrizione: str = None) -> dict:
        """
        Verifica lo stato del database dopo un errore per determinare se l'operazione è stata comunque completata.
        Può anche verificare che specifici campi abbiano valori attesi.

        Args:
            tabella: Nome della tabella da verificare
            condizioni: Dizionario con le condizioni di ricerca (campo: valore)
            campi_da_verificare: Dizionario con i campi da verificare e i loro valori attesi (campo: valore)
            descrizione: Descrizione dell'operazione per il logging

        Returns:
            dict: Dizionario con 'esiste' (bool) e 'valori_corretti' (bool)
        """
        desc = f" ({descrizione})" if descrizione else ""
        result = {'esiste': False, 'valori_corretti': False, 'record': None}

        try:
            # Costruisci la query di verifica
            if campi_da_verificare:
                # Se ci sono campi da verificare, seleziona quei campi specifici
                select_fields = ", ".join(list(campi_da_verificare.keys()))
                query = f"SELECT {select_fields} FROM {tabella} WHERE "
            else:
                # Altrimenti, conta solo i record
                query = f"SELECT COUNT(*) FROM {tabella} WHERE "

            # Costruisci le condizioni WHERE
            conditions = [f"{campo} = %s" for campo in condizioni.keys()]
            params = list(condizioni.values())
            query += " AND ".join(conditions)

            # Esegui la query
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, params)

                    if campi_da_verificare:
                        # Verifica campi specifici
                        record = cursor.fetchone()
                        if not record:
                            logging.info(f"⚠️ Verifica post-errore{desc}: record non trovato")
                            return result

                        result['esiste'] = True
                        result['record'] = record

                        # Verifica i valori dei campi
                        valori_corretti = True
                        for campo, valore_atteso in campi_da_verificare.items():
                            # Estrai il valore effettivo (gestendo sia dict che tuple)
                            valore_effettivo = record[campo] if isinstance(record, dict) else record[0]

                            # Confronta i valori (convertendo in stringhe per sicurezza)
                            if str(valore_effettivo) != str(valore_atteso):
                                valori_corretti = False
                                logging.warning(f"⚠️ Campo {campo} ha valore {valore_effettivo} invece di {valore_atteso}")
                                break

                        result['valori_corretti'] = valori_corretti

                        if valori_corretti:
                            logging.info(f"✅ Verifica post-errore{desc}: operazione completata con successo e valori corretti")
                        else:
                            logging.warning(f"⚠️ Verifica post-errore{desc}: record trovato ma valori non corretti")
                    else:
                        # Verifica solo l'esistenza del record
                        count = cursor.fetchone()[0]
                        result['esiste'] = count > 0

                        if result['esiste']:
                            logging.info(f"✅ Verifica post-errore{desc}: operazione completata con successo nonostante l'errore")
                        else:
                            logging.info(f"⚠️ Verifica post-errore{desc}: operazione non completata")

            return result

        except Exception as e:
            logging.error(f"❌ Errore durante la verifica post-errore{desc}: {str(e)}")
            return result
