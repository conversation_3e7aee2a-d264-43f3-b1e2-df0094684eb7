import logging
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager

# Import del modulo responsabili per l'auto-inserimento
try:
    from modules.responsabili import ottieni_o_crea_responsabile
except ImportError:
    # Fallback se il modulo non è disponibile
    def ottieni_o_crea_responsabile(nome, id_cantiere, email=None, telefono=None):
        logging.warning("⚠️ Modulo responsabili non disponibile, auto-inserimento disabilitato")
        return None

# Import del modulo notifiche per l'invio automatico
try:
    from modules.notifiche import invia_notifica_comanda
except ImportError:
    # Fallback se il modulo non è disponibile
    def invia_notifica_comanda(*args, **kwargs):
        logging.warning("⚠️ Modulo notifiche non disponibile, notifiche disabilitate")
        return {'email': False, 'sms': False}

# Configura il logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

@contextmanager
def database_connection():
    """Context manager per gestire connessioni al database in modo sicuro."""
    conn = None
    try:
        # Connessione a PostgreSQL
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        # Impostazione del cursor factory per avere un comportamento simile a sqlite3.Row
        conn.cursor_factory = RealDictCursor
        yield conn
    except psycopg2.Error as e:
        logging.error(f"❌ Errore database: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def genera_codice_comanda(id_cantiere: int, tipo_comanda: str) -> str:
    """
    Genera un codice univoco per una comanda con formato semplice e leggibile.
    Formato: POS001, CPT002, CAR003, etc.

    Args:
        id_cantiere: ID del cantiere
        tipo_comanda: Tipo di comanda ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE', 'TESTING')

    Returns:
        str: Codice univoco generato (es. POS001)
    """
    # Genera un prefisso in base al tipo di comanda
    prefisso = ""
    if tipo_comanda == "POSA":
        prefisso = "POS"
    elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
        prefisso = "CPT"
    elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
        prefisso = "CAR"
    elif tipo_comanda in ["CERTIFICAZIONE", "TESTING"]:
        prefisso = "CER"
    else:
        prefisso = "COM"

    # Trova il prossimo numero progressivo per questo tipo di comanda nel cantiere
    try:
        with database_connection() as conn:
            c = conn.cursor()

            # Cerca l'ultimo numero per questo prefisso nel cantiere
            c.execute("""
                SELECT codice_comanda
                FROM Comande
                WHERE id_cantiere = %s
                AND codice_comanda LIKE %s
                ORDER BY codice_comanda DESC
                LIMIT 1
            """, (id_cantiere, f"{prefisso}%"))

            result = c.fetchone()

            if result:
                ultimo_codice = result['codice_comanda']
                try:
                    # Estrae il numero dal codice (es. POS001 -> 1)
                    ultimo_numero = int(ultimo_codice[3:])
                    nuovo_numero = ultimo_numero + 1
                except (ValueError, IndexError):
                    nuovo_numero = 1
            else:
                nuovo_numero = 1

            # Genera il codice con formato a 3 cifre (001, 002, etc.)
            codice_comanda = f"{prefisso}{nuovo_numero:03d}"

            return codice_comanda

    except Exception as e:
        logging.error(f"Errore nella generazione del codice comanda: {str(e)}")
        # Fallback: usa timestamp ridotto se il database non è disponibile
        timestamp_short = datetime.now().strftime('%H%M%S')
        return f"{prefisso}{timestamp_short}"

def ottieni_nome_cantiere(id_cantiere: int) -> str:
    """
    Ottiene il nome del cantiere dal database.

    Args:
        id_cantiere: ID del cantiere

    Returns:
        Nome del cantiere o stringa vuota se non trovato
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT commessa FROM Cantieri WHERE id_cantiere = %s", (id_cantiere,))
            result = c.fetchone()
            return result['commessa'] if result else f"Cantiere {id_cantiere}"
    except Exception as e:
        logging.error(f"❌ Errore nel recupero nome cantiere {id_cantiere}: {str(e)}")
        return f"Cantiere {id_cantiere}"

def crea_comanda(id_cantiere: int, tipo_comanda: str, descrizione: str,
                responsabile: str, data_scadenza: Optional[date] = None,
                responsabile_email: Optional[str] = None, responsabile_telefono: Optional[str] = None,
                numero_componenti_squadra: int = 1) -> Optional[str]:
    """
    Crea una nuova comanda e restituisce il codice univoco generato.
    Gestisce automaticamente l'inserimento del responsabile se non esiste.

    Args:
        id_cantiere: ID del cantiere
        tipo_comanda: Tipo di comanda ('POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE', 'TESTING')
        descrizione: Descrizione della comanda
        responsabile: Nome del responsabile
        data_scadenza: Data di scadenza (opzionale)
        responsabile_email: Email del responsabile (opzionale)
        responsabile_telefono: Telefono del responsabile (opzionale)

    Returns:
        Optional[str]: Il codice univoco generato o None in caso di errore
    """
    try:
        # Normalizza il tipo comanda (TESTING -> CERTIFICAZIONE)
        if tipo_comanda == "TESTING":
            tipo_comanda = "CERTIFICAZIONE"

        # Auto-inserimento responsabile se forniti email o telefono
        if responsabile_email or responsabile_telefono:
            logging.info(f"📝 Gestione auto-inserimento responsabile: {responsabile}")
            responsabile_data = ottieni_o_crea_responsabile(
                nome_responsabile=responsabile,
                id_cantiere=id_cantiere,
                email=responsabile_email,
                telefono=responsabile_telefono
            )

            if responsabile_data:
                logging.info(f"✅ Responsabile gestito: ID {responsabile_data.get('id_responsabile', 'N/A')}")
            else:
                logging.warning(f"⚠️ Impossibile gestire il responsabile {responsabile}")

        # Genera un codice univoco
        codice_comanda = genera_codice_comanda(id_cantiere, tipo_comanda)

        # Inserisci la comanda nel database
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                INSERT INTO Comande (
                    codice_comanda, tipo_comanda, descrizione, data_creazione,
                    data_scadenza, responsabile, stato, id_cantiere, numero_componenti_squadra
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                codice_comanda, tipo_comanda, descrizione, datetime.now().date(),
                data_scadenza, responsabile, "CREATA", id_cantiere, numero_componenti_squadra
            ))
            conn.commit()  # Commit esplicito

        # Invia notifica automatica se disponibili dati di contatto
        if responsabile_email or responsabile_telefono:
            try:
                cantiere_nome = ottieni_nome_cantiere(id_cantiere)
                risultati_notifica = invia_notifica_comanda(
                    codice_comanda=codice_comanda,
                    tipo_comanda=tipo_comanda,
                    responsabile_nome=responsabile,
                    responsabile_email=responsabile_email,
                    responsabile_telefono=responsabile_telefono,
                    descrizione=descrizione,
                    cantiere_nome=cantiere_nome
                )

                if risultati_notifica['email']:
                    logging.info(f"📧 Notifica email inviata per comanda {codice_comanda}")
                if risultati_notifica['sms']:
                    logging.info(f"📱 Notifica SMS inviata per comanda {codice_comanda}")

            except Exception as e:
                logging.warning(f"⚠️ Errore nell'invio notifica per comanda {codice_comanda}: {str(e)}")

        logging.info(f"✅ Creata comanda con codice: {codice_comanda}")
        return codice_comanda
    
    except Exception as e:
        logging.error(f"❌ Errore nella creazione della comanda: {str(e)}")
        return None

def assegna_cavi_a_comanda(codice_comanda: str, lista_id_cavi: List[str]) -> bool:
    """
    Assegna una lista di cavi a una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        lista_id_cavi: Lista di ID dei cavi da assegnare
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Ottieni il tipo di comanda e l'ID del cantiere
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return False
                
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            # Aggiorna i cavi in base al tipo di comanda
            cavi_assegnati = 0
            for id_cavo in lista_id_cavi:
                if tipo_comanda == "POSA":
                    # Verifica che il cavo non sia già assegnato a una comanda di posa
                    c.execute("SELECT comanda_posa, stato_installazione FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                             (id_cavo, id_cantiere))
                    result = c.fetchone()
                    if result and result['comanda_posa']:
                        logging.warning(f"⚠️ Il cavo {id_cavo} è già assegnato alla comanda di posa {result['comanda_posa']}")
                        continue

                    # Aggiorna il cavo con la comanda e imposta stato "In corso"
                    c.execute("UPDATE Cavi SET comanda_posa = %s, stato_installazione = %s WHERE id_cavo = %s AND id_cantiere = %s",
                             (codice_comanda, "In corso", id_cavo, id_cantiere))
                    cavi_assegnati += 1
                    logging.info(f"✅ Cavo {id_cavo} assegnato a comanda POSA {codice_comanda} - stato: In corso")

                elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                    # Verifica che il cavo non sia già assegnato a una comanda di collegamento partenza
                    c.execute("SELECT comanda_partenza, stato_installazione FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                             (id_cavo, id_cantiere))
                    result = c.fetchone()
                    if result and result['comanda_partenza']:
                        logging.warning(f"⚠️ Il cavo {id_cavo} è già assegnato alla comanda di collegamento partenza {result['comanda_partenza']}")
                        continue

                    # Per collegamento partenza, il cavo deve essere installato
                    if result and result['stato_installazione'] != "Installato":
                        logging.warning(f"⚠️ Il cavo {id_cavo} deve essere installato prima di poter essere collegato")
                        continue

                    # Aggiorna il cavo con la comanda e imposta stato "In corso"
                    c.execute("UPDATE Cavi SET comanda_partenza = %s, stato_installazione = %s WHERE id_cavo = %s AND id_cantiere = %s",
                             (codice_comanda, "In corso", id_cavo, id_cantiere))
                    cavi_assegnati += 1
                    logging.info(f"✅ Cavo {id_cavo} assegnato a comanda COLLEGAMENTO_PARTENZA {codice_comanda} - stato: In corso")

                elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                    # Verifica che il cavo non sia già assegnato a una comanda di collegamento arrivo
                    c.execute("SELECT comanda_arrivo, stato_installazione FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                             (id_cavo, id_cantiere))
                    result = c.fetchone()
                    if result and result['comanda_arrivo']:
                        logging.warning(f"⚠️ Il cavo {id_cavo} è già assegnato alla comanda di collegamento arrivo {result['comanda_arrivo']}")
                        continue

                    # Per collegamento arrivo, il cavo deve essere installato
                    if result and result['stato_installazione'] != "Installato":
                        logging.warning(f"⚠️ Il cavo {id_cavo} deve essere installato prima di poter essere collegato")
                        continue

                    # Aggiorna il cavo con la comanda e imposta stato "In corso"
                    c.execute("UPDATE Cavi SET comanda_arrivo = %s, stato_installazione = %s WHERE id_cavo = %s AND id_cantiere = %s",
                             (codice_comanda, "In corso", id_cavo, id_cantiere))
                    cavi_assegnati += 1
                    logging.info(f"✅ Cavo {id_cavo} assegnato a comanda COLLEGAMENTO_ARRIVO {codice_comanda} - stato: In corso")

                elif tipo_comanda == "CERTIFICAZIONE":
                    # Verifica che il cavo non sia già assegnato a una comanda di certificazione
                    c.execute("SELECT comanda_certificazione, stato_installazione FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                             (id_cavo, id_cantiere))
                    result = c.fetchone()
                    if result and result['comanda_certificazione']:
                        logging.warning(f"⚠️ Il cavo {id_cavo} è già assegnato alla comanda di certificazione {result['comanda_certificazione']}")
                        continue

                    # Per certificazione, il cavo deve essere installato (prerequisito: solo installazione richiesta)
                    if result and result['stato_installazione'] != "Installato":
                        logging.warning(f"⚠️ Il cavo {id_cavo} deve essere installato prima di poter essere certificato")
                        continue

                    # Aggiorna il cavo con la comanda e imposta stato "In corso"
                    c.execute("UPDATE Cavi SET comanda_certificazione = %s, stato_installazione = %s WHERE id_cavo = %s AND id_cantiere = %s",
                             (codice_comanda, "In corso", id_cavo, id_cantiere))
                    cavi_assegnati += 1
                    logging.info(f"✅ Cavo {id_cavo} assegnato a comanda CERTIFICAZIONE {codice_comanda} - stato: In corso")
            
            # Aggiorna lo stato della comanda solo se sono stati assegnati cavi
            if cavi_assegnati > 0:
                c.execute("UPDATE Comande SET stato = 'ASSEGNATA' WHERE codice_comanda = %s", (codice_comanda,))
                conn.commit()  # Commit esplicito
                logging.info(f"✅ Assegnati {cavi_assegnati} cavi alla comanda {codice_comanda}")
                return True
            else:
                logging.warning(f"⚠️ Nessun cavo assegnato alla comanda {codice_comanda}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nell'assegnazione dei cavi alla comanda: {str(e)}")
        return False

def ottieni_cavi_comanda(codice_comanda: str) -> List[Dict[str, Any]]:
    """
    Ottiene la lista dei cavi assegnati a una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        
    Returns:
        List[Dict[str, Any]]: Lista di dizionari con i dati dei cavi
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Ottieni il tipo di comanda e l'ID del cantiere
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return []
                
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            # Query corretta con mappatura campi corretta
            base_query = """
                SELECT
                    id_cavo,
                    COALESCE(NULLIF(TRIM(tipologia), ''), 'Non specificata') as tipologia,
                    COALESCE(NULLIF(TRIM(sezione), ''), 'Non specificata') as formazione,
                    COALESCE(metri_teorici, 0.0) as metratura_teorica,
                    metratura_reale,
                    COALESCE(NULLIF(TRIM(stato_installazione), ''), 'Non Installato') as stato_installazione,
                    COALESCE(collegamenti, 0) as collegamenti,
                    CASE WHEN EXISTS (
                        SELECT 1 FROM certificazioni_cavi cc
                        WHERE cc.id_cavo = Cavi.id_cavo AND cc.id_cantiere = Cavi.id_cantiere
                    ) THEN true ELSE false END as certificato
                FROM Cavi
                WHERE id_cantiere = %s
            """

            # Query in base al tipo di comanda
            if tipo_comanda == "POSA":
                query = base_query + " AND comanda_posa = %s"
            elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                query = base_query + " AND comanda_partenza = %s"
            elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                query = base_query + " AND comanda_arrivo = %s"
            elif tipo_comanda == "CERTIFICAZIONE":
                query = base_query + " AND comanda_certificazione = %s"
            else:
                logging.error(f"❌ Tipo comanda non riconosciuto: {tipo_comanda}")
                return []

            c.execute(query, (id_cantiere, codice_comanda))
            
            return [dict(row) for row in c.fetchall()]
            
    except Exception as e:
        logging.error(f"❌ Errore nel recupero dei cavi della comanda: {str(e)}")
        return []

def aggiorna_dati_posa(codice_comanda: str, dati_posa: Dict[str, Dict[str, Any]]) -> bool:
    """
    Aggiorna i dati di posa per i cavi di una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        dati_posa: Dizionario con id_cavo come chiave e dati di posa come valore
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Verifica che sia una comanda di posa
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result or result['tipo_comanda'] != "POSA":
                logging.error(f"❌ La comanda {codice_comanda} non è una comanda di posa")
                return False
            
            id_cantiere = result['id_cantiere']
            
            # Aggiorna i dati di posa per ogni cavo
            cavi_aggiornati = 0
            for id_cavo, dati in dati_posa.items():
                c.execute("""
                    UPDATE Cavi
                    SET metratura_reale = %s,
                        data_posa = %s,
                        stato_installazione = 'Installato',
                        responsabile_posa = %s,
                        comanda_posa = NULL
                    WHERE id_cavo = %s AND id_cantiere = %s AND comanda_posa = %s
                """, (
                    dati.get('metratura_reale', 0),
                    dati.get('data_posa', datetime.now().date()),
                    dati.get('responsabile_posa', ''),
                    id_cavo, id_cantiere, codice_comanda
                ))
                
                if c.rowcount > 0:
                    cavi_aggiornati += 1
            
            # Aggiorna lo stato della comanda solo se sono stati aggiornati cavi
            if cavi_aggiornati > 0:
                c.execute("UPDATE Comande SET stato = 'COMPLETATA' WHERE codice_comanda = %s", (codice_comanda,))
                conn.commit()  # Commit esplicito
                logging.info(f"✅ Aggiornati dati di posa per {cavi_aggiornati} cavi della comanda {codice_comanda}")
                return True
            else:
                logging.warning(f"⚠️ Nessun cavo aggiornato per la comanda {codice_comanda}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento dei dati di posa: {str(e)}")
        return False

def aggiorna_dati_collegamento(codice_comanda: str, dati_collegamento: Dict[str, Dict[str, Any]]) -> bool:
    """
    Aggiorna i dati di collegamento per i cavi di una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        dati_collegamento: Dizionario con id_cavo come chiave e dati di collegamento come valore
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Verifica che sia una comanda di collegamento
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return False
            
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            if not (tipo_comanda == "COLLEGAMENTO_PARTENZA" or tipo_comanda == "COLLEGAMENTO_ARRIVO"):
                logging.error(f"❌ La comanda {codice_comanda} non è una comanda di collegamento")
                return False
            
            # Aggiorna i dati di collegamento per ogni cavo
            cavi_aggiornati = 0
            for id_cavo, dati in dati_collegamento.items():
                if tipo_comanda == "COLLEGAMENTO_PARTENZA":
                    # Aggiorna il flag collegamenti per indicare che il lato partenza è collegato
                    c.execute("""
                        UPDATE Cavi
                        SET collegamenti = CASE
                                WHEN collegamenti = 0 THEN 1  -- Nessun collegamento -> Partenza
                                WHEN collegamenti = 2 THEN 3  -- Arrivo -> Entrambi
                                ELSE collegamenti
                            END,
                            responsabile_partenza = %s,
                            stato_installazione = 'Installato',
                            comanda_partenza = NULL
                        WHERE id_cavo = %s AND id_cantiere = %s AND comanda_partenza = %s
                    """, (
                        dati.get('responsabile', ''),
                        id_cavo, id_cantiere, codice_comanda
                    ))
                else:  # COLLEGAMENTO_ARRIVO
                    # Aggiorna il flag collegamenti per indicare che il lato arrivo è collegato
                    c.execute("""
                        UPDATE Cavi
                        SET collegamenti = CASE
                                WHEN collegamenti = 0 THEN 2  -- Nessun collegamento -> Arrivo
                                WHEN collegamenti = 1 THEN 3  -- Partenza -> Entrambi
                                ELSE collegamenti
                            END,
                            responsabile_arrivo = %s,
                            stato_installazione = 'Installato',
                            comanda_arrivo = NULL
                        WHERE id_cavo = %s AND id_cantiere = %s AND comanda_arrivo = %s
                    """, (
                        dati.get('responsabile', ''),
                        id_cavo, id_cantiere, codice_comanda
                    ))
                
                if c.rowcount > 0:
                    cavi_aggiornati += 1
            
            # Aggiorna lo stato della comanda solo se sono stati aggiornati cavi
            if cavi_aggiornati > 0:
                c.execute("UPDATE Comande SET stato = 'COMPLETATA' WHERE codice_comanda = %s", (codice_comanda,))
                conn.commit()  # Commit esplicito
                logging.info(f"✅ Aggiornati dati di collegamento per {cavi_aggiornati} cavi della comanda {codice_comanda}")
                return True
            else:
                logging.warning(f"⚠️ Nessun cavo aggiornato per la comanda {codice_comanda}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento dei dati di collegamento: {str(e)}")
        return False

def ottieni_comande_cantiere(id_cantiere: int, stato: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Ottiene la lista delle comande di un cantiere, opzionalmente filtrate per stato.
    
    Args:
        id_cantiere: ID del cantiere
        stato: Stato delle comande da filtrare (opzionale)
        
    Returns:
        List[Dict[str, Any]]: Lista di dizionari con i dati delle comande
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Query con o senza filtro per stato
            if stato:
                c.execute("""
                    SELECT * FROM Comande 
                    WHERE id_cantiere = %s AND stato = %s
                    ORDER BY data_creazione DESC
                """, (id_cantiere, stato))
            else:
                c.execute("""
                    SELECT * FROM Comande 
                    WHERE id_cantiere = %s
                    ORDER BY data_creazione DESC
                """, (id_cantiere,))
            
            return [dict(row) for row in c.fetchall()]
            
    except Exception as e:
        logging.error(f"❌ Errore nel recupero delle comande: {str(e)}")
        return []

def ottieni_dettagli_comanda(codice_comanda: str) -> Optional[Dict[str, Any]]:
    """
    Ottiene i dettagli di una comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        
    Returns:
        Optional[Dict[str, Any]]: Dizionario con i dettagli della comanda o None se non trovata
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            c.execute("SELECT * FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            
            if not result:
                return None
                
            return dict(result)
            
    except Exception as e:
        logging.error(f"❌ Errore nel recupero dei dettagli della comanda: {str(e)}")
        return None

def elimina_comanda(codice_comanda: str) -> bool:
    """
    Elimina una comanda e rimuove i riferimenti dai cavi.
    
    Args:
        codice_comanda: Codice univoco della comanda
        
    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Ottieni il tipo di comanda e l'ID del cantiere
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result:
                logging.error(f"❌ Comanda non trovata: {codice_comanda}")
                return False
                
            tipo_comanda = result['tipo_comanda']
            id_cantiere = result['id_cantiere']
            
            # Rimuovi i riferimenti dai cavi e ripristina lo stato precedente
            if tipo_comanda == "POSA":
                # Per POSA: rimuovi comanda e ripristina stato "Da installare"
                c.execute("UPDATE Cavi SET comanda_posa = NULL, stato_installazione = 'Da installare' WHERE comanda_posa = %s AND id_cantiere = %s",
                         (codice_comanda, id_cantiere))
            elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                # Per COLLEGAMENTO_PARTENZA: rimuovi comanda e ripristina stato "Installato"
                c.execute("UPDATE Cavi SET comanda_partenza = NULL, stato_installazione = 'Installato' WHERE comanda_partenza = %s AND id_cantiere = %s",
                         (codice_comanda, id_cantiere))
            elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                # Per COLLEGAMENTO_ARRIVO: rimuovi comanda e ripristina stato "Installato"
                c.execute("UPDATE Cavi SET comanda_arrivo = NULL, stato_installazione = 'Installato' WHERE comanda_arrivo = %s AND id_cantiere = %s",
                         (codice_comanda, id_cantiere))
            elif tipo_comanda == "CERTIFICAZIONE":
                # Per CERTIFICAZIONE: rimuovi comanda e ripristina stato "Installato"
                c.execute("UPDATE Cavi SET comanda_certificazione = NULL, stato_installazione = 'Installato' WHERE comanda_certificazione = %s AND id_cantiere = %s",
                         (codice_comanda, id_cantiere))
            
            # Elimina la comanda
            c.execute("DELETE FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            conn.commit()  # Commit esplicito

            logging.info(f"✅ Eliminata comanda {codice_comanda}")
            return True
            
    except Exception as e:
        logging.error(f"❌ Errore nell'eliminazione della comanda: {str(e)}")
        return False

def aggiorna_dati_certificazione(codice_comanda: str, dati_certificazione: Dict[str, Any]) -> bool:
    """
    Aggiorna i dati di certificazione per i cavi di una comanda.

    Args:
        codice_comanda: Codice univoco della comanda
        dati_certificazione: Dizionario con i dati di certificazione

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            # Verifica che sia una comanda di certificazione
            c.execute("SELECT tipo_comanda, id_cantiere FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
            result = c.fetchone()
            if not result or result['tipo_comanda'] != "CERTIFICAZIONE":
                logging.error(f"❌ La comanda {codice_comanda} non è una comanda di certificazione")
                return False

            id_cantiere = result['id_cantiere']

            # Aggiorna i dettagli generali della certificazione nella comanda
            dettagli_certificazione = {
                'esito_complessivo': dati_certificazione.get('esito_complessivo', 'Da Verificare'),
                'data_certificazione': dati_certificazione.get('data_certificazione', datetime.now().date().isoformat()),
                'note_generali': dati_certificazione.get('note_generali', ''),
                'responsabile_certificazione': dati_certificazione.get('responsabile_certificazione', '')
            }

            c.execute("""
                UPDATE Comande
                SET dettagli_certificazione = %s, data_completamento = %s
                WHERE codice_comanda = %s
            """, (
                json.dumps(dettagli_certificazione),
                datetime.now().date(),
                codice_comanda
            ))

            # Aggiorna i dati di certificazione per ogni cavo specifico
            cavi_aggiornati = 0
            dati_cavi = dati_certificazione.get('dati_cavi', {})

            for id_cavo, dati_cavo in dati_cavi.items():
                c.execute("""
                    UPDATE Cavi
                    SET stato_certificazione = %s,
                        data_certificazione_cavo = %s,
                        stato_installazione = 'Installato',
                        comanda_certificazione = NULL
                    WHERE id_cavo = %s AND id_cantiere = %s AND comanda_certificazione = %s
                """, (
                    dati_cavo.get('stato_certificazione', 'Da Certificare'),
                    dati_cavo.get('data_certificazione_cavo', datetime.now().date()),
                    id_cavo, id_cantiere, codice_comanda
                ))

                if c.rowcount > 0:
                    cavi_aggiornati += 1

            # Aggiorna lo stato della comanda solo se sono stati aggiornati cavi
            if cavi_aggiornati > 0 or dati_certificazione.get('forza_completamento', False):
                c.execute("UPDATE Comande SET stato = 'COMPLETATA' WHERE codice_comanda = %s", (codice_comanda,))
                conn.commit()  # Commit esplicito
                logging.info(f"✅ Aggiornati dati di certificazione per {cavi_aggiornati} cavi della comanda {codice_comanda}")
                return True
            else:
                logging.warning(f"⚠️ Nessun cavo aggiornato per la comanda {codice_comanda}")
                return False

    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento dei dati di certificazione: {str(e)}")
        return False

def cambia_stato_comanda(codice_comanda: str, nuovo_stato: str) -> bool:
    """
    Cambia lo stato di una comanda.

    Args:
        codice_comanda: Codice univoco della comanda
        nuovo_stato: Nuovo stato della comanda

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            c.execute("UPDATE Comande SET stato = %s WHERE codice_comanda = %s", (nuovo_stato, codice_comanda))

            if c.rowcount > 0:
                conn.commit()  # Commit esplicito
                logging.info(f"✅ Stato della comanda {codice_comanda} cambiato in {nuovo_stato}")
                return True
            else:
                logging.warning(f"⚠️ Comanda non trovata: {codice_comanda}")
                return False

    except Exception as e:
        logging.error(f"❌ Errore nel cambio di stato della comanda: {str(e)}")
        return False


def crea_comanda_con_cavi(id_cantiere: int, tipo_comanda: str, descrizione: str,
                         responsabile: str, lista_id_cavi: List[str],
                         data_scadenza: Optional[date] = None,
                         responsabile_email: Optional[str] = None,
                         responsabile_telefono: Optional[str] = None,
                         numero_componenti_squadra: int = 1) -> Optional[str]:
    """
    Crea una nuova comanda e assegna immediatamente i cavi specificati.
    Flusso migliorato: selezione cavi -> creazione comanda -> assegnazione automatica.
    Gestisce automaticamente l'inserimento del responsabile se non esiste.

    Args:
        id_cantiere: ID del cantiere
        tipo_comanda: Tipo di comanda
        descrizione: Descrizione della comanda
        responsabile: Nome del responsabile
        lista_id_cavi: Lista degli ID dei cavi da assegnare
        data_scadenza: Data di scadenza (opzionale)
        responsabile_email: Email del responsabile (opzionale)
        responsabile_telefono: Telefono del responsabile (opzionale)

    Returns:
        Optional[str]: Il codice univoco generato o None in caso di errore
    """
    try:
        # Normalizza il tipo comanda (TESTING -> CERTIFICAZIONE)
        if tipo_comanda == "TESTING":
            tipo_comanda = "CERTIFICAZIONE"

        # Verifica che i cavi esistano e siano disponibili
        with database_connection() as conn:
            c = conn.cursor()

            # Controlla disponibilità cavi
            cavi_disponibili = []
            for id_cavo in lista_id_cavi:
                c.execute("SELECT id_cavo FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                         (id_cavo, id_cantiere))
                if c.fetchone():
                    # Verifica che il cavo non sia già assegnato per questo tipo di comanda
                    if tipo_comanda == "POSA":
                        c.execute("SELECT comanda_posa FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                                 (id_cavo, id_cantiere))
                        result = c.fetchone()
                        if not result or not result['comanda_posa']:
                            cavi_disponibili.append(id_cavo)
                    elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                        c.execute("SELECT comanda_partenza FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                                 (id_cavo, id_cantiere))
                        result = c.fetchone()
                        if not result or not result['comanda_partenza']:
                            cavi_disponibili.append(id_cavo)
                    elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                        c.execute("SELECT comanda_arrivo FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                                 (id_cavo, id_cantiere))
                        result = c.fetchone()
                        if not result or not result['comanda_arrivo']:
                            cavi_disponibili.append(id_cavo)
                    elif tipo_comanda in ["CERTIFICAZIONE", "TESTING"]:
                        c.execute("SELECT comanda_certificazione FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s",
                                 (id_cavo, id_cantiere))
                        result = c.fetchone()
                        if not result or not result['comanda_certificazione']:
                            cavi_disponibili.append(id_cavo)

            if not cavi_disponibili:
                logging.warning(f"⚠️ Nessun cavo disponibile per la comanda di tipo {tipo_comanda}")
                return None

            # Crea la comanda
            codice_comanda = crea_comanda(id_cantiere, tipo_comanda, descrizione, responsabile,
                                        data_scadenza, responsabile_email, responsabile_telefono,
                                        numero_componenti_squadra)

            if not codice_comanda:
                logging.error("❌ Errore nella creazione della comanda")
                return None

            # Assegna i cavi disponibili
            success = assegna_cavi_a_comanda(codice_comanda, cavi_disponibili)

            if success:
                logging.info(f"✅ Comanda {codice_comanda} creata e {len(cavi_disponibili)} cavi assegnati")
                if len(cavi_disponibili) < len(lista_id_cavi):
                    logging.warning(f"⚠️ Solo {len(cavi_disponibili)} di {len(lista_id_cavi)} cavi sono stati assegnati")
                return codice_comanda
            else:
                # Se l'assegnazione fallisce, elimina la comanda creata
                elimina_comanda(codice_comanda)
                logging.error("❌ Errore nell'assegnazione dei cavi, comanda eliminata")
                return None

    except Exception as e:
        logging.error(f"❌ Errore nella creazione della comanda con cavi: {str(e)}")
        return None


def ottieni_cavi_disponibili_per_tipo_comanda(id_cantiere: int, tipo_comanda: str) -> List[Dict[str, Any]]:
    """
    Ottiene la lista dei cavi disponibili per un determinato tipo di comanda.

    Args:
        id_cantiere: ID del cantiere
        tipo_comanda: Tipo di comanda

    Returns:
        List[Dict]: Lista dei cavi disponibili con le loro informazioni
    """
    try:
        # Normalizza il tipo comanda
        if tipo_comanda == "TESTING":
            tipo_comanda = "CERTIFICAZIONE"

        with database_connection() as conn:
            c = conn.cursor()

            # Query base per tutti i cavi del cantiere
            base_query = "SELECT * FROM Cavi WHERE id_cantiere = %s"

            # Aggiungi condizioni specifiche per tipo comanda
            if tipo_comanda == "POSA":
                query = base_query + " AND (comanda_posa IS NULL OR comanda_posa = '')"
            elif tipo_comanda == "COLLEGAMENTO_PARTENZA":
                query = base_query + " AND (comanda_partenza IS NULL OR comanda_partenza = '') AND comanda_posa IS NOT NULL"
            elif tipo_comanda == "COLLEGAMENTO_ARRIVO":
                query = base_query + " AND (comanda_arrivo IS NULL OR comanda_arrivo = '') AND comanda_posa IS NOT NULL"
            elif tipo_comanda in ["CERTIFICAZIONE", "TESTING"]:
                query = base_query + " AND (comanda_certificazione IS NULL OR comanda_certificazione = '') AND comanda_posa IS NOT NULL"
            else:
                query = base_query

            c.execute(query, (id_cantiere,))
            cavi = c.fetchall()

            return [dict(cavo) for cavo in cavi]

    except Exception as e:
        logging.error(f"❌ Errore nel recupero dei cavi disponibili: {str(e)}")
        return []