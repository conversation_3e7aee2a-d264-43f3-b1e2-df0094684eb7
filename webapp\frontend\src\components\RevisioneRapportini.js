import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Card, 
  CardContent, 
  Button, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import { 
  CheckCircle as ApprovaIcon, 
  Cancel as RifiutaIcon,
  Visibility as VisualizzaIcon,
  Print as StampaIcon
} from '@mui/icons-material';

const RevisioneRapportini = ({ idCantiere }) => {
  const [rapportini, setRapportini] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dialogRevisione, setDialogRevisione] = useState(false);
  const [rapportinoSelezionato, setRapportinoSelezionato] = useState(null);
  const [azioneRevisione, setAzioneRevisione] = useState('');
  const [noteRevisione, setNoteRevisione] = useState('');
  const [metriCorretti, setMetriCorretti] = useState('');
  const [bobinaCorretta, setBobinaCorretta] = useState('');
  const [statistiche, setStatistiche] = useState({});

  useEffect(() => {
    caricaRapportini();
    caricaStatistiche();
  }, [idCantiere]);

  const caricaRapportini = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/rapportini/in-attesa?id_cantiere=${idCantiere}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Errore nel caricamento dei rapportini');
      }

      const data = await response.json();
      setRapportini(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const caricaStatistiche = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/rapportini/statistiche/${idCantiere}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStatistiche(data);
      }
    } catch (err) {
      console.error('Errore nel caricamento delle statistiche:', err);
    }
  };

  const apriDialogRevisione = (rapportino, azione) => {
    setRapportinoSelezionato(rapportino);
    setAzioneRevisione(azione);
    setNoteRevisione('');
    setMetriCorretti(rapportino.metri_posati || '');
    setBobinaCorretta(rapportino.bobina_utilizzata || '');
    setDialogRevisione(true);
  };

  const confermaRevisione = async () => {
    try {
      const token = localStorage.getItem('token');
      const payload = {
        id_rapportino: rapportinoSelezionato.id_rapportino,
        azione: azioneRevisione,
        note_revisione: noteRevisione
      };

      if (azioneRevisione === 'APPROVA') {
        if (metriCorretti !== rapportinoSelezionato.metri_posati) {
          payload.metri_posati_corretti = parseFloat(metriCorretti);
        }
        if (bobinaCorretta !== rapportinoSelezionato.bobina_utilizzata) {
          payload.bobina_utilizzata_corretta = bobinaCorretta;
        }
      }

      const response = await fetch('/api/rapportini/revisiona', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error('Errore nella revisione del rapportino');
      }

      setDialogRevisione(false);
      caricaRapportini();
      caricaStatistiche();
    } catch (err) {
      setError(err.message);
    }
  };

  const generaRapportinoStampa = async (codiceComanda) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/rapportini/genera-stampa', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          codice_comanda: codiceComanda,
          formato_pagina: 'A4',
          includi_dettagli_cavi: true,
          includi_note_lavoro: true
        })
      });

      if (!response.ok) {
        throw new Error('Errore nella generazione del rapportino');
      }

      const data = await response.json();
      // Apri il PDF in una nuova finestra
      window.open(data.url_pdf, '_blank');
    } catch (err) {
      setError(err.message);
    }
  };

  const getStatoChip = (stato) => {
    const colori = {
      'IN_ATTESA': { color: 'warning', label: 'In Attesa' },
      'APPROVATO': { color: 'success', label: 'Approvato' },
      'RIFIUTATO': { color: 'error', label: 'Rifiutato' }
    };
    
    const config = colori[stato] || { color: 'default', label: stato };
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Revisione Rapportini Mobile App
      </Typography>

      {/* Statistiche */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Totale Rapportini
              </Typography>
              <Typography variant="h4">
                {statistiche.totale_rapportini || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                In Attesa
              </Typography>
              <Typography variant="h4" color="warning.main">
                {statistiche.in_attesa_revisione || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Approvati
              </Typography>
              <Typography variant="h4" color="success.main">
                {statistiche.approvati || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Rifiutati
              </Typography>
              <Typography variant="h4" color="error.main">
                {statistiche.rifiutati || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabella rapportini */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Comanda</TableCell>
              <TableCell>Cavo</TableCell>
              <TableCell>Attività</TableCell>
              <TableCell>Metri Posati</TableCell>
              <TableCell>Bobina</TableCell>
              <TableCell>Responsabile</TableCell>
              <TableCell>Data Lavoro</TableCell>
              <TableCell>Stato</TableCell>
              <TableCell>Azioni</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rapportini.map((rapportino) => (
              <TableRow key={rapportino.id_rapportino}>
                <TableCell>{rapportino.codice_comanda}</TableCell>
                <TableCell>{rapportino.id_cavo}</TableCell>
                <TableCell>{rapportino.attivita_svolta}</TableCell>
                <TableCell>{rapportino.metri_posati || 'N/A'}</TableCell>
                <TableCell>{rapportino.bobina_utilizzata || 'N/A'}</TableCell>
                <TableCell>{rapportino.responsabile}</TableCell>
                <TableCell>
                  {new Date(rapportino.data_lavoro).toLocaleDateString('it-IT')}
                </TableCell>
                <TableCell>{getStatoChip(rapportino.stato_revisione)}</TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    {rapportino.stato_revisione === 'IN_ATTESA' && (
                      <>
                        <Button
                          size="small"
                          color="success"
                          startIcon={<ApprovaIcon />}
                          onClick={() => apriDialogRevisione(rapportino, 'APPROVA')}
                        >
                          Approva
                        </Button>
                        <Button
                          size="small"
                          color="error"
                          startIcon={<RifiutaIcon />}
                          onClick={() => apriDialogRevisione(rapportino, 'RIFIUTA')}
                        >
                          Rifiuta
                        </Button>
                      </>
                    )}
                    <Button
                      size="small"
                      startIcon={<StampaIcon />}
                      onClick={() => generaRapportinoStampa(rapportino.codice_comanda)}
                    >
                      Stampa
                    </Button>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {rapportini.length === 0 && (
        <Box textAlign="center" py={4}>
          <Typography color="textSecondary">
            Nessun rapportino in attesa di revisione
          </Typography>
        </Box>
      )}

      {/* Dialog Revisione */}
      <Dialog open={dialogRevisione} onClose={() => setDialogRevisione(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {azioneRevisione === 'APPROVA' ? 'Approva Rapportino' : 'Rifiuta Rapportino'}
        </DialogTitle>
        <DialogContent>
          {rapportinoSelezionato && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Dettagli Rapportino
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography><strong>Comanda:</strong> {rapportinoSelezionato.codice_comanda}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography><strong>Cavo:</strong> {rapportinoSelezionato.id_cavo}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography><strong>Attività:</strong> {rapportinoSelezionato.attivita_svolta}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography><strong>Responsabile:</strong> {rapportinoSelezionato.responsabile}</Typography>
                </Grid>
              </Grid>

              {azioneRevisione === 'APPROVA' && (
                <Box mt={3}>
                  <Typography variant="h6" gutterBottom>
                    Verifica e Correzione Dati
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Metri Posati"
                        type="number"
                        value={metriCorretti}
                        onChange={(e) => setMetriCorretti(e.target.value)}
                        helperText="Correggi se necessario"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Bobina Utilizzata"
                        value={bobinaCorretta}
                        onChange={(e) => setBobinaCorretta(e.target.value)}
                        helperText="Correggi se necessario"
                      />
                    </Grid>
                  </Grid>
                </Box>
              )}

              <Box mt={3}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Note di Revisione"
                  value={noteRevisione}
                  onChange={(e) => setNoteRevisione(e.target.value)}
                  placeholder="Inserisci eventuali note per il responsabile..."
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogRevisione(false)}>
            Annulla
          </Button>
          <Button 
            onClick={confermaRevisione}
            color={azioneRevisione === 'APPROVA' ? 'success' : 'error'}
            variant="contained"
          >
            {azioneRevisione === 'APPROVA' ? 'Approva' : 'Rifiuta'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RevisioneRapportini;
