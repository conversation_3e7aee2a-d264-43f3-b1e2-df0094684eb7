import apiClient from './apiClient';

class TipologieCaviService {
  // ==================== CATEGORIE ====================
  
  async getCategorie(includeInactive = false, livello = null) {
    const params = new URLSearchParams();
    if (includeInactive) params.append('include_inactive', 'true');
    if (livello !== null) params.append('livello', livello.toString());
    
    const response = await apiClient.get(`/admin/tipologie-cavi/categorie?${params}`);
    return response.data;
  }

  async createCategoria(categoriaData) {
    const response = await apiClient.post('/admin/tipologie-cavi/categorie', categoriaData);
    return response.data;
  }

  async updateCategoria(categoriaId, categoriaData) {
    const response = await apiClient.put(`/admin/tipologie-cavi/categorie/${categoriaId}`, categoriaData);
    return response.data;
  }

  async deleteCategoria(categoriaId) {
    const response = await apiClient.delete(`/admin/tipologie-cavi/categorie/${categoriaId}`);
    return response.data;
  }

  // ==================== PRODUTTORI ====================
  
  async getProduttori(includeInactive = false, paese = null) {
    const params = new URLSearchParams();
    if (includeInactive) params.append('include_inactive', 'true');
    if (paese) params.append('paese', paese);
    
    const response = await apiClient.get(`/admin/tipologie-cavi/produttori?${params}`);
    return response.data;
  }

  async createProduttore(produttoreData) {
    const response = await apiClient.post('/admin/tipologie-cavi/produttori', produttoreData);
    return response.data;
  }

  async updateProduttore(produttoreId, produttoreData) {
    const response = await apiClient.put(`/admin/tipologie-cavi/produttori/${produttoreId}`, produttoreData);
    return response.data;
  }

  async deleteProduttore(produttoreId) {
    const response = await apiClient.delete(`/admin/tipologie-cavi/produttori/${produttoreId}`);
    return response.data;
  }

  // ==================== STANDARD ====================
  
  async getStandard(includeInactive = false, enteNormativo = null) {
    const params = new URLSearchParams();
    if (includeInactive) params.append('include_inactive', 'true');
    if (enteNormativo) params.append('ente_normativo', enteNormativo);
    
    const response = await apiClient.get(`/admin/tipologie-cavi/standard?${params}`);
    return response.data;
  }

  async createStandard(standardData) {
    const response = await apiClient.post('/admin/tipologie-cavi/standard', standardData);
    return response.data;
  }

  async updateStandard(standardId, standardData) {
    const response = await apiClient.put(`/admin/tipologie-cavi/standard/${standardId}`, standardData);
    return response.data;
  }

  async deleteStandard(standardId) {
    const response = await apiClient.delete(`/admin/tipologie-cavi/standard/${standardId}`);
    return response.data;
  }

  // ==================== TIPOLOGIE ====================
  
  async getTipologie(filters = {}) {
    const params = new URLSearchParams();
    
    // Parametri di paginazione
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.page_size) params.append('page_size', filters.page_size.toString());
    
    // Filtri
    if (filters.categoria_id) params.append('categoria_id', filters.categoria_id.toString());
    if (filters.produttore_id) params.append('produttore_id', filters.produttore_id.toString());
    if (filters.disponibile !== null && filters.disponibile !== undefined) {
      params.append('disponibile', filters.disponibile.toString());
    }
    if (filters.search_text) params.append('search_text', filters.search_text);
    
    const response = await apiClient.get(`/admin/tipologie-cavi/tipologie?${params}`);
    return response.data;
  }

  async getTipologia(tipologiaId) {
    const response = await apiClient.get(`/admin/tipologie-cavi/tipologie/${tipologiaId}`);
    return response.data;
  }

  async createTipologia(tipologiaData) {
    const response = await apiClient.post('/admin/tipologie-cavi/tipologie', tipologiaData);
    return response.data;
  }

  async updateTipologia(tipologiaId, tipologiaData) {
    const response = await apiClient.put(`/admin/tipologie-cavi/tipologie/${tipologiaId}`, tipologiaData);
    return response.data;
  }

  async deleteTipologia(tipologiaId) {
    const response = await apiClient.delete(`/admin/tipologie-cavi/tipologie/${tipologiaId}`);
    return response.data;
  }

  // ==================== SPECIFICHE TECNICHE ====================
  
  async getSpecificheTipologia(tipologiaId) {
    const response = await apiClient.get(`/admin/tipologie-cavi/tipologie/${tipologiaId}/specifiche`);
    return response.data;
  }

  async createSpecifica(tipologiaId, specificaData) {
    const response = await apiClient.post(`/admin/tipologie-cavi/tipologie/${tipologiaId}/specifiche`, specificaData);
    return response.data;
  }

  async updateSpecifica(tipologiaId, specificaId, specificaData) {
    const response = await apiClient.put(`/admin/tipologie-cavi/tipologie/${tipologiaId}/specifiche/${specificaId}`, specificaData);
    return response.data;
  }

  async deleteSpecifica(tipologiaId, specificaId) {
    const response = await apiClient.delete(`/admin/tipologie-cavi/tipologie/${tipologiaId}/specifiche/${specificaId}`);
    return response.data;
  }

  // ==================== UTILITÀ ====================
  
  /**
   * Cerca tipologie di cavi per testo libero
   */
  async searchTipologie(searchText, limit = 10) {
    const filters = {
      search_text: searchText,
      page: 1,
      page_size: limit,
      disponibile: true
    };
    
    const result = await this.getTipologie(filters);
    return result.tipologie;
  }

  /**
   * Ottiene le categorie in formato albero gerarchico
   */
  async getCategorieTree() {
    const categorie = await this.getCategorie();
    
    // Organizza le categorie in struttura ad albero
    const categorieMap = new Map();
    const tree = [];
    
    // Prima passata: crea la mappa
    categorie.forEach(categoria => {
      categorieMap.set(categoria.id_categoria, {
        ...categoria,
        children: []
      });
    });
    
    // Seconda passata: costruisce l'albero
    categorie.forEach(categoria => {
      const node = categorieMap.get(categoria.id_categoria);
      if (categoria.id_categoria_padre) {
        const parent = categorieMap.get(categoria.id_categoria_padre);
        if (parent) {
          parent.children.push(node);
        }
      } else {
        tree.push(node);
      }
    });
    
    return tree;
  }

  /**
   * Ottiene le tipologie compatibili con una specifica categoria
   */
  async getTipologieByCategoria(categoriaId, includeSubcategories = true) {
    let categorieIds = [categoriaId];
    
    if (includeSubcategories) {
      const categorie = await this.getCategorie();
      const subcategories = categorie.filter(cat => cat.id_categoria_padre === categoriaId);
      categorieIds = categorieIds.concat(subcategories.map(cat => cat.id_categoria));
    }
    
    const allTipologie = [];
    for (const catId of categorieIds) {
      const result = await this.getTipologie({ categoria_id: catId, disponibile: true });
      allTipologie.push(...result.tipologie);
    }
    
    return allTipologie;
  }

  /**
   * Ottiene statistiche sulle tipologie di cavi
   */
  async getStatistiche() {
    try {
      const [categorie, produttori, standard, tipologieResult] = await Promise.all([
        this.getCategorie(),
        this.getProduttori(),
        this.getStandard(),
        this.getTipologie({ page: 1, page_size: 1 }) // Solo per il conteggio
      ]);

      return {
        totale_categorie: categorie.length,
        categorie_attive: categorie.filter(c => c.attiva).length,
        totale_produttori: produttori.length,
        produttori_attivi: produttori.filter(p => p.attivo).length,
        totale_standard: standard.length,
        standard_attivi: standard.filter(s => s.attivo).length,
        totale_tipologie: tipologieResult.total_count,
        tipologie_disponibili: tipologieResult.total_count // Questo andrebbe calcolato con un filtro specifico
      };
    } catch (error) {
      console.error('Errore nel calcolo delle statistiche:', error);
      throw error;
    }
  }

  /**
   * Valida i dati di una tipologia prima del salvataggio
   */
  validateTipologia(tipologiaData) {
    const errors = [];

    if (!tipologiaData.codice_prodotto?.trim()) {
      errors.push('Il codice prodotto è obbligatorio');
    }

    if (!tipologiaData.id_categoria) {
      errors.push('La categoria è obbligatoria');
    }

    if (tipologiaData.temperatura_min_celsius !== null && 
        tipologiaData.temperatura_max_celsius !== null &&
        tipologiaData.temperatura_min_celsius >= tipologiaData.temperatura_max_celsius) {
      errors.push('La temperatura massima deve essere maggiore di quella minima');
    }

    if (tipologiaData.prezzo_indicativo_euro_per_metro !== null && 
        tipologiaData.prezzo_indicativo_euro_per_metro < 0) {
      errors.push('Il prezzo non può essere negativo');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Formatta una tipologia per la visualizzazione
   */
  formatTipologiaForDisplay(tipologia) {
    return {
      ...tipologia,
      display_name: tipologia.nome_commerciale || tipologia.codice_prodotto,
      full_description: `${tipologia.codice_prodotto}${tipologia.nome_commerciale ? ` - ${tipologia.nome_commerciale}` : ''}`,
      produttore_nome: tipologia.produttore?.nome_produttore || 'Non specificato',
      categoria_nome: tipologia.categoria?.nome_categoria || 'Non specificata',
      prezzo_formattato: tipologia.prezzo_indicativo_euro_per_metro 
        ? `€ ${tipologia.prezzo_indicativo_euro_per_metro.toFixed(4)}/m`
        : 'Non disponibile'
    };
  }
}

const tipologieCaviService = new TipologieCaviService();
export default tipologieCaviService;
